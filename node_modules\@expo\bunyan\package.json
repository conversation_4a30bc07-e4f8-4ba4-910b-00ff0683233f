{"name": "@expo/bunyan", "version": "4.0.0", "description": "a JSON logging library for node.js services", "author": "<PERSON> <<EMAIL>> (http://trentm.com)", "main": "./lib/bunyan.js", "types": "./types/index.d.ts", "files": ["types", "lib"], "repository": {"type": "git", "url": "git://github.com/trentm/node-bunyan.git"}, "engines": ["node >=0.10.0"], "keywords": ["log", "logging", "log4j", "json", "bunyan"], "license": "MIT", "dependencies": {"uuid": "^8.0.0"}, "// mv": "required for RotatingFileStream", "optionalDependencies": {"mv": "~2", "safe-json-stringify": "~1"}, "devDependencies": {"nodeunit": "0.9", "ben": "0.0.0", "markdown-toc": "0.12.x", "verror": "1.3.3", "vasync": "1.4.3"}, "scripts": {"test": "make test"}}