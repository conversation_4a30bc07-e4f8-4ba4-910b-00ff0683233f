"use strict";

function _createExoticTransformer() {
  const data = require("./createExoticTransformer");
  _createExoticTransformer = function () {
    return data;
  };
  return data;
}
// Copyright 2021-present 650 Industries (Expo). All rights reserved.

module.exports = (0, _createExoticTransformer().createExoticTransformer)({
  nodeModulesPaths: ['node_modules']
});
//# sourceMappingURL=metro-expo-exotic-babel-transformer.js.map