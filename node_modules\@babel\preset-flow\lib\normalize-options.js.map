{"version": 3, "names": ["_helperValidatorOption", "require", "v", "OptionValidator", "normalizeOptions", "options", "all", "ignoreExtensions", "experimental_useHermesParser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../src/normalize-options.ts"], "sourcesContent": ["import { OptionValidator } from \"@babel/helper-validator-option\";\nconst v = new OptionValidator(\"@babel/preset-flow\");\n\nexport default function normalizeOptions(options: any = {}) {\n  let { all, ignoreExtensions, experimental_useHermesParser } = options;\n  const { allowDeclareFields } = options;\n\n  if (process.env.BABEL_8_BREAKING) {\n    v.invariant(\n      !(\"allowDeclareFields\" in options),\n      `Since Babel 8, \\`declare property: A\\` is always supported, and the \"allowDeclareFields\" option is no longer available. Please remove it from your config.`,\n    );\n    const TopLevelOptions = {\n      all: \"all\",\n      ignoreExtensions: \"ignoreExtensions\",\n      experimental_useHermesParser: \"experimental_useHermesParser\",\n    };\n    v.validateTopLevelOptions(options, TopLevelOptions);\n    all = v.validateBooleanOption(TopLevelOptions.all, all);\n    ignoreExtensions = v.validateBooleanOption(\n      TopLevelOptions.ignoreExtensions,\n      ignoreExtensions,\n    );\n    experimental_useHermesParser = v.validateBooleanOption(\n      TopLevelOptions.experimental_useHermesParser,\n      experimental_useHermesParser,\n    );\n    return {\n      all,\n      ignoreExtensions,\n      experimental_useHermesParser,\n    };\n  } else {\n    return {\n      all,\n      allowDeclareFields,\n      ignoreExtensions,\n      experimental_useHermesParser,\n    };\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AACA,MAAMC,CAAC,GAAG,IAAIC,sCAAe,CAAC,oBAAoB,CAAC;AAEpC,SAASC,gBAAgBA,CAACC,OAAY,GAAG,CAAC,CAAC,EAAE;EAC1D,IAAI;IAAEC,GAAG;IAAEC,gBAAgB;IAAEC;EAA6B,CAAC,GAAGH,OAAO;EACrE,MAAM;IAAEI;EAAmB,CAAC,GAAGJ,OAAO;EA2B/B;IACL,OAAO;MACLC,GAAG;MACHG,kBAAkB;MAClBF,gBAAgB;MAChBC;IACF,CAAC;EACH;AACF", "ignoreList": []}