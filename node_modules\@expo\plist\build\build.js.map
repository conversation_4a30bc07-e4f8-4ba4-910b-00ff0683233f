{"version": 3, "file": "build.js", "sourceRoot": "", "sources": ["../src/build.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;;;;;;;;;;;;;;;;;;;kCAuBkC;;;;;;AAElC,0DAA+B;AAC/B,4DAAoC;AAEpC;;;;;;GAMG;AAEH,SAAS,aAAa,CAAC,CAAO;IAC5B,SAAS,GAAG,CAAC,CAAS;QACpB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,CACL,CAAC,CAAC,cAAc,EAAE;QAClB,GAAG;QACH,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QACxB,GAAG;QACH,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACnB,GAAG;QACH,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACpB,GAAG;QACH,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACtB,GAAG;QACH,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACtB,GAAG,CACJ,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC3C,SAAS,IAAI,CAAC,GAAW;IACvB,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACtD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AAED;;;;;;;GAOG;AAEH,SAAgB,KAAK,CAAC,GAAQ,EAAE,IAA6B;IAC3D,MAAM,MAAM,GAAG;QACb,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,OAAO;KAC4C,CAAC;IAEhE,MAAM,MAAM,GAAG;QACb,KAAK,EAAE,6BAA6B;QACpC,KAAK,EAAE,gDAAgD;KACxD,CAAC;IAEF,MAAM,GAAG,GAAG,oBAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAEvC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5D,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAE1B,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAEnB,IAAI,CAAC,IAAI;QAAE,IAAI,GAAG,EAAE,CAAC;IACrB,6BAA6B;IAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;IACpC,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,CAAC;AAvBD,sBAuBC;AAED;;;;;GAKG;AAEH,SAAS,QAAQ,CAAC,IAAS,EAAE,UAAe;IAC1C,IAAI,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAExB,IAAI,IAAI,IAAI,WAAW,EAAE;KACxB;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC9B,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;SAC/B;KACF;SAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAChC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;KACrD;SAAM,IAAI,IAAI,IAAI,QAAQ,EAAE;QAC3B,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,KAAK,IAAI,IAAI,IAAI,EAAE;YACjB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;gBACzD,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC;aAClC;SACF;KACF;SAAM,IAAI,IAAI,IAAI,QAAQ,EAAE;QAC3B,uCAAuC;QACvC,gEAAgE;QAChE,QAAQ,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QAC/C,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC/C;SAAM,IAAI,IAAI,IAAI,MAAM,EAAE;QACzB,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAC3D;SAAM,IAAI,IAAI,IAAI,SAAS,EAAE;QAC5B,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;KACzC;SAAM,IAAI,IAAI,IAAI,QAAQ,EAAE;QAC3B,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACpC;SAAM,IAAI,IAAI,IAAI,aAAa,EAAE;QAChC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,mBAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;KACxD;SAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,aAAa,EAAE;QACpE,gBAAgB;QAChB,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,mBAAM,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAC/E;AACH,CAAC", "sourcesContent": ["/* eslint-disable */\n/* (The MIT License)\n\nCopyright (c) 2010-2017 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE. */\n\nimport base64 from 'base64-js';\nimport xmlbuilder from 'xmlbuilder';\n\n/**\n * Accepts a `Date` instance and returns an ISO date string.\n *\n * @param {Date} d - Date instance to serialize\n * @returns {String} ISO date string representation of `d`\n * @api private\n */\n\nfunction ISODateString(d: Date): string {\n  function pad(n: number) {\n    return n < 10 ? '0' + n : n;\n  }\n  return (\n    d.getUTCFullYear() +\n    '-' +\n    pad(d.getUTCMonth() + 1) +\n    '-' +\n    pad(d.getUTCDate()) +\n    'T' +\n    pad(d.getUTCHours()) +\n    ':' +\n    pad(d.getUTCMinutes()) +\n    ':' +\n    pad(d.getUTCSeconds()) +\n    'Z'\n  );\n}\n\n/**\n * Returns the internal \"type\" of `obj` via the\n * `Object.prototype.toString()` trick.\n *\n * @param {Mixed} obj - any value\n * @returns {String} the internal \"type\" name\n * @api private\n */\n\nconst toString = Object.prototype.toString;\nfunction type(obj: object): string | null {\n  const m = toString.call(obj).match(/\\[object (.*)\\]/);\n  return m ? m[1] : m;\n}\n\n/**\n * Generate an XML plist string from the input object `obj`.\n *\n * @param {Object} obj - the object to convert\n * @param {Object} [opts] - optional options object\n * @returns {String} converted plist XML string\n * @api public\n */\n\nexport function build(obj: any, opts?: { [key: string]: any }): string {\n  const XMLHDR = {\n    version: '1.0',\n    encoding: 'UTF-8',\n  } as { version: string; encoding: string; standalone: boolean };\n\n  const XMLDTD = {\n    pubid: '-//Apple//DTD PLIST 1.0//EN',\n    sysid: 'http://www.apple.com/DTDs/PropertyList-1.0.dtd',\n  };\n\n  const doc = xmlbuilder.create('plist');\n\n  doc.dec(XMLHDR.version, XMLHDR.encoding, XMLHDR.standalone);\n  doc.dtd(XMLDTD.pubid, XMLDTD.sysid);\n  doc.att('version', '1.0');\n\n  walk_obj(obj, doc);\n\n  if (!opts) opts = {};\n  // default `pretty` to `true`\n  opts.pretty = opts.pretty !== false;\n  return doc.end(opts);\n}\n\n/**\n * depth first, recursive traversal of a javascript object. when complete,\n * next_child contains a reference to the build XML object.\n *\n * @api private\n */\n\nfunction walk_obj(next: any, next_child: any): void {\n  let tag_type, i, prop;\n  const name = type(next);\n\n  if (name == 'Undefined') {\n  } else if (Array.isArray(next)) {\n    next_child = next_child.ele('array');\n    for (i = 0; i < next.length; i++) {\n      walk_obj(next[i], next_child);\n    }\n  } else if (Buffer.isBuffer(next)) {\n    next_child.ele('data').raw(next.toString('base64'));\n  } else if (name == 'Object') {\n    next_child = next_child.ele('dict');\n    for (prop in next) {\n      if (next.hasOwnProperty(prop) && next[prop] !== undefined) {\n        next_child.ele('key').txt(prop);\n        walk_obj(next[prop], next_child);\n      }\n    }\n  } else if (name == 'Number') {\n    // detect if this is an integer or real\n    // TODO: add an ability to force one way or another via a \"cast\"\n    tag_type = next % 1 === 0 ? 'integer' : 'real';\n    next_child.ele(tag_type).txt(next.toString());\n  } else if (name == 'Date') {\n    next_child.ele('date').txt(ISODateString(new Date(next)));\n  } else if (name == 'Boolean') {\n    next_child.ele(next ? 'true' : 'false');\n  } else if (name == 'String') {\n    next_child.ele('string').txt(next);\n  } else if (name == 'ArrayBuffer') {\n    next_child.ele('data').raw(base64.fromByteArray(next));\n  } else if (next && next.buffer && type(next.buffer) == 'ArrayBuffer') {\n    // a typed array\n    next_child.ele('data').raw(base64.fromByteArray(new Uint8Array(next.buffer)));\n  }\n}\n"]}