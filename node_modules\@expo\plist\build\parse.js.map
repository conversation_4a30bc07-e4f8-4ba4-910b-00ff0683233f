{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../src/parse.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;;;;;;;;;;;;;;;;;;;kCAuBkC;;;;;;AAElC,2CAA2C;AAC3C,oDAA4B;AAE5B,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB;;;;;;;GAOG;AAEH,SAAS,gBAAgB,CAAC,IAA4B;IACpD,OAAO,CACL,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,CAC9F,CAAC;AACJ,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,WAAW,CAAC,IAA4B;IAC/C,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;GAMG;AAEH,SAAgB,KAAK,CAAC,GAAW;IAC/B,mDAAmD;IACnD,MAAM,GAAG,GAAG,IAAI,kBAAS,CAAC,EAAE,YAAY,KAAI,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACtE,IAAA,gBAAM,EACJ,GAAG,CAAC,eAAe,CAAC,QAAQ,KAAK,OAAO,EACxC,qDAAqD,CACtD,CAAC;IACF,IAAI,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAE/C,sDAAsD;IACtD,mCAAmC;IACnC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;QAAE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAExC,OAAO,KAAK,CAAC;AACf,CAAC;AAdD,sBAcC;AAED;;;;;;GAMG;AAEH,SAAS,aAAa,CAAC,IAA4B;IACjD,IAAI,CAAC,EAAE,OAA+B,EAAE,GAAG,EAAE,OAAc,EAAE,GAAG,EAAE,OAAO,CAAC;IAE1E,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QAC7B,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC;SAChB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjD;SACF;QACD,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,OAAO,GAAG,EAAE,CAAC;QACb,GAAG,GAAG,IAAI,CAAC;QACX,OAAO,GAAG,CAAC,CAAC;QACZ,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC;SAChB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAAE,SAAS;YACnD,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;gBACrB,IAAA,gBAAM,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,EAAE,oCAAoC,CAAC,CAAC;gBACpF,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;iBAAM;gBACL,IAAA,gBAAM,EACJ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,EACrC,kBAAkB,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,0BAA0B,CACpF,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;YACD,OAAO,IAAI,CAAC,CAAC;SACd;QACD,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,GAAG,GAAG,yBAAyB,CAAC,CAAC;SAC1E;QACD,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QACpC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC;SAChB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,GAAG,IAAI,IAAI;oBAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACpC;SACF;QACD,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QACpC,4DAA4D;KAC7D;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;QAClC,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,EAAE,CAAC;SACX;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KACrC;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACrC,GAAG,GAAG,EAAE,CAAC;QACT,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC;SACZ;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACzC,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,EAAE;gBAC7C,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACrC;SACF;QACD,OAAO,GAAG,CAAC;KACZ;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;QACtC,IAAA,gBAAM,EAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAC1D,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;KACnD;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAA,gBAAM,EAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACvD,GAAG,GAAG,EAAE,CAAC;QACT,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC7C,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACrC;SACF;QACD,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;KACxB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,GAAG,GAAG,EAAE,CAAC;QACT,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SACnC;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC7C,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aACzD;SACF;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;KACnC;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAA,gBAAM,EAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACvD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;KAC/C;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;AACH,CAAC", "sourcesContent": ["/* eslint-disable */\n/* (The MIT License)\n\nCopyright (c) 2010-2017 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE. */\n\nimport { DOMParser } from '@xmldom/xmldom';\nimport assert from 'assert';\n\nconst TEXT_NODE = 3;\nconst CDATA_NODE = 4;\nconst COMMENT_NODE = 8;\n\n/**\n * We ignore raw text (usually whitespace), <!-- xml comments -->,\n * and raw CDATA nodes.\n *\n * @param {Element} node\n * @returns {Boolean}\n * @api private\n */\n\nfunction shouldIgnoreNode(node: { [key: string]: any }): boolean {\n  return (\n    node.nodeType === TEXT_NODE || node.nodeType === COMMENT_NODE || node.nodeType === CDATA_NODE\n  );\n}\n\n/**\n * Check if the node is empty. Some plist file has such node:\n * <key />\n * this node shoud be ignored.\n *\n * @see https://github.com/TooTallNate/plist.js/issues/66\n * @param {Element} node\n * @returns {Boolean}\n * @api private\n */\nfunction isEmptyNode(node: { [key: string]: any }): boolean {\n  return !node.childNodes || node.childNodes.length === 0;\n}\n\n/**\n * Parses a Plist XML string. Returns an Object.\n *\n * @param {String} xml - the XML String to decode\n * @returns {Mixed} the decoded value from the Plist XML\n * @api public\n */\n\nexport function parse(xml: string): any {\n  // prevent the parser from logging non-fatel errors\n  const doc = new DOMParser({ errorHandler() {} }).parseFromString(xml);\n  assert(\n    doc.documentElement.nodeName === 'plist',\n    'malformed document. First element should be <plist>'\n  );\n  let plist = parsePlistXML(doc.documentElement);\n\n  // the root <plist> node gets interpreted as an Array,\n  // so pull out the inner data first\n  if (plist.length == 1) plist = plist[0];\n\n  return plist;\n}\n\n/**\n * Convert an XML based plist document into a JSON representation.\n *\n * @param {Object} xml_node - current XML node in the plist\n * @returns {Mixed} built up JSON object\n * @api private\n */\n\nfunction parsePlistXML(node: { [key: string]: any }): any {\n  let i, new_obj: { [key: string]: any }, key, new_arr: any[], res, counter;\n\n  if (!node) return null;\n\n  if (node.nodeName === 'plist') {\n    new_arr = [];\n    if (isEmptyNode(node)) {\n      return new_arr;\n    }\n    for (i = 0; i < node.childNodes.length; i++) {\n      if (!shouldIgnoreNode(node.childNodes[i])) {\n        new_arr.push(parsePlistXML(node.childNodes[i]));\n      }\n    }\n    return new_arr;\n  } else if (node.nodeName === 'dict') {\n    new_obj = {};\n    key = null;\n    counter = 0;\n    if (isEmptyNode(node)) {\n      return new_obj;\n    }\n    for (i = 0; i < node.childNodes.length; i++) {\n      if (shouldIgnoreNode(node.childNodes[i])) continue;\n      if (counter % 2 === 0) {\n        assert(node.childNodes[i].nodeName === 'key', 'Missing key while parsing <dict/>.');\n        key = parsePlistXML(node.childNodes[i]);\n      } else {\n        assert(\n          node.childNodes[i].nodeName !== 'key',\n          'Unexpected key \"' + parsePlistXML(node.childNodes[i]) + '\" while parsing <dict/>.'\n        );\n        new_obj[key] = parsePlistXML(node.childNodes[i]);\n      }\n      counter += 1;\n    }\n    if (counter % 2 === 1) {\n      throw new Error('Missing value for \"' + key + '\" while parsing <dict/>');\n    }\n    return new_obj;\n  } else if (node.nodeName === 'array') {\n    new_arr = [];\n    if (isEmptyNode(node)) {\n      return new_arr;\n    }\n    for (i = 0; i < node.childNodes.length; i++) {\n      if (!shouldIgnoreNode(node.childNodes[i])) {\n        res = parsePlistXML(node.childNodes[i]);\n        if (res != null) new_arr.push(res);\n      }\n    }\n    return new_arr;\n  } else if (node.nodeName === '#text') {\n    // TODO: what should we do with text types? (CDATA sections)\n  } else if (node.nodeName === 'key') {\n    if (isEmptyNode(node)) {\n      return '';\n    }\n    return node.childNodes[0].nodeValue;\n  } else if (node.nodeName === 'string') {\n    res = '';\n    if (isEmptyNode(node)) {\n      return res;\n    }\n    for (i = 0; i < node.childNodes.length; i++) {\n      const type = node.childNodes[i].nodeType;\n      if (type === TEXT_NODE || type === CDATA_NODE) {\n        res += node.childNodes[i].nodeValue;\n      }\n    }\n    return res;\n  } else if (node.nodeName === 'integer') {\n    assert(!isEmptyNode(node), 'Cannot parse \"\" as integer.');\n    return parseInt(node.childNodes[0].nodeValue, 10);\n  } else if (node.nodeName === 'real') {\n    assert(!isEmptyNode(node), 'Cannot parse \"\" as real.');\n    res = '';\n    for (i = 0; i < node.childNodes.length; i++) {\n      if (node.childNodes[i].nodeType === TEXT_NODE) {\n        res += node.childNodes[i].nodeValue;\n      }\n    }\n    return parseFloat(res);\n  } else if (node.nodeName === 'data') {\n    res = '';\n    if (isEmptyNode(node)) {\n      return Buffer.from(res, 'base64');\n    }\n    for (i = 0; i < node.childNodes.length; i++) {\n      if (node.childNodes[i].nodeType === TEXT_NODE) {\n        res += node.childNodes[i].nodeValue.replace(/\\s+/g, '');\n      }\n    }\n    return Buffer.from(res, 'base64');\n  } else if (node.nodeName === 'date') {\n    assert(!isEmptyNode(node), 'Cannot parse \"\" as Date.');\n    return new Date(node.childNodes[0].nodeValue);\n  } else if (node.nodeName === 'true') {\n    return true;\n  } else if (node.nodeName === 'false') {\n    return false;\n  }\n}\n"]}