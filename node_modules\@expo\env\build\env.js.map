{"version": 3, "file": "env.js", "names": ["_chalk", "data", "_interopRequireDefault", "require", "dotenv", "_interopRequireWildcard", "_dotenvExpand", "fs", "_getenv", "path", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "debug", "isEnabled", "boolish", "createControlledEnvironment", "IS_DEBUG", "enabled", "userDefinedEnvironment", "undefined", "memo", "_get<PERSON>orce", "projectRoot", "options", "env", "files", "process", "dotenvFiles", "getFiles", "NODE_ENV", "loadedEnvFiles", "parsed", "for<PERSON>ach", "dotenvFile", "absoluteDotenvFile", "resolve", "existsSync", "results", "expand", "config", "override", "push", "keys", "_userDefinedEnvironme", "error", "Error", "console", "message", "length", "force", "load", "envInfo", "log", "chalk", "gray", "map", "file", "basename", "join", "mode", "silent", "red", "includes", "filter", "Boolean"], "sources": ["../src/env.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport chalk from 'chalk';\nimport * as dotenv from 'dotenv';\nimport { expand } from 'dotenv-expand';\nimport * as fs from 'fs';\nimport { boolish } from 'getenv';\nimport * as path from 'path';\n\ntype LoadOptions = {\n  silent?: boolean;\n  force?: boolean;\n};\n\nconst debug = require('debug')('expo:env') as typeof console.log;\n\nexport function isEnabled(): boolean {\n  return !boolish('EXPO_NO_DOTENV', false);\n}\n\nexport function createControlledEnvironment() {\n  const IS_DEBUG = require('debug').enabled('expo:env');\n\n  let userDefinedEnvironment: NodeJS.ProcessEnv | undefined = undefined;\n  let memo: { env: NodeJS.ProcessEnv; files: string[] } | undefined = undefined;\n\n  function _getForce(\n    projectRoot: string,\n    options: LoadOptions = {}\n  ): { env: Record<string, string | undefined>; files: string[] } {\n    if (!isEnabled()) {\n      debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n      return { env: {}, files: [] };\n    }\n\n    if (!userDefinedEnvironment) {\n      userDefinedEnvironment = { ...process.env };\n    }\n\n    // https://github.com/bkeepers/dotenv#what-other-env-files-can-i-use\n    const dotenvFiles = getFiles(process.env.NODE_ENV, options);\n\n    const loadedEnvFiles: string[] = [];\n    const parsed: dotenv.DotenvParseOutput = {};\n\n    // Load environment variables from .env* files. Suppress warnings using silent\n    // if this file is missing. dotenv will never modify any environment variables\n    // that have already been set. Variable expansion is supported in .env files.\n    // https://github.com/motdotla/dotenv\n    // https://github.com/motdotla/dotenv-expand\n    dotenvFiles.forEach((dotenvFile) => {\n      const absoluteDotenvFile = path.resolve(projectRoot, dotenvFile);\n      if (!fs.existsSync(absoluteDotenvFile)) {\n        return;\n      }\n      try {\n        const results = expand(\n          dotenv.config({\n            debug: IS_DEBUG,\n            path: absoluteDotenvFile,\n            // We will handle overriding ourselves to allow for HMR.\n            override: true,\n          })\n        );\n        if (results.parsed) {\n          loadedEnvFiles.push(absoluteDotenvFile);\n          debug(`Loaded environment variables from: ${absoluteDotenvFile}`);\n\n          for (const key of Object.keys(results.parsed || {})) {\n            if (\n              typeof parsed[key] === 'undefined' &&\n              // Custom override logic to prevent overriding variables that\n              // were set before the CLI process began.\n              typeof userDefinedEnvironment?.[key] === 'undefined'\n            ) {\n              parsed[key] = results.parsed[key];\n            }\n          }\n        } else {\n          debug(`Failed to load environment variables from: ${absoluteDotenvFile}`);\n        }\n      } catch (error: unknown) {\n        if (error instanceof Error) {\n          console.error(\n            `Failed to load environment variables from ${absoluteDotenvFile}: ${error.message}`\n          );\n        } else {\n          throw error;\n        }\n      }\n    });\n\n    if (!loadedEnvFiles.length) {\n      debug(`No environment variables loaded from .env files.`);\n    }\n\n    return { env: parsed, files: loadedEnvFiles };\n  }\n\n  /** Get the environment variables without mutating the environment. This returns memoized values unless the `force` property is provided. */\n  function get(\n    projectRoot: string,\n    options: LoadOptions = {}\n  ): { env: Record<string, string | undefined>; files: string[] } {\n    if (!isEnabled()) {\n      debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n      return { env: {}, files: [] };\n    }\n    if (!options.force && memo) {\n      return memo;\n    }\n    memo = _getForce(projectRoot, options);\n    return memo;\n  }\n\n  /** Load environment variables from .env files and mutate the current `process.env` with the results. */\n  function load(projectRoot: string, options: LoadOptions = {}) {\n    if (!isEnabled()) {\n      debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n      return process.env;\n    }\n\n    const envInfo = get(projectRoot, options);\n\n    if (!options.force) {\n      const keys = Object.keys(envInfo.env);\n      if (keys.length) {\n        console.log(\n          chalk.gray('env: load', envInfo.files.map((file) => path.basename(file)).join(' '))\n        );\n        console.log(chalk.gray('env: export', keys.join(' ')));\n      }\n    }\n\n    process.env = { ...process.env, ...envInfo.env };\n    return process.env;\n  }\n\n  return {\n    load,\n    get,\n    _getForce,\n  };\n}\n\nexport function getFiles(\n  mode: string | undefined,\n  { silent = false }: Pick<LoadOptions, 'silent'> = {}\n): string[] {\n  if (!isEnabled()) {\n    debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n    return [];\n  }\n\n  if (!mode) {\n    if (silent) {\n      debug('NODE_ENV is not defined, proceeding without mode-specific .env');\n    } else {\n      console.error(\n        chalk.red(\n          'The NODE_ENV environment variable is required but was not specified. Ensure the project is bundled with Expo CLI or NODE_ENV is set.'\n        )\n      );\n      console.error(chalk.red('Proceeding without mode-specific .env'));\n    }\n  }\n\n  if (mode && !['development', 'test', 'production'].includes(mode)) {\n    throw new Error(\n      `Environment variable \"NODE_ENV=${mode}\" is invalid. Valid values are \"development\", \"test\", and \"production`\n    );\n  }\n\n  if (!mode) {\n    // Support environments that don't respect NODE_ENV\n    return [`.env.local`, '.env'];\n  }\n  // https://github.com/bkeepers/dotenv#what-other-env-files-can-i-use\n  const dotenvFiles = [\n    `.env.${mode}.local`,\n    // Don't include `.env.local` for `test` environment\n    // since normally you expect tests to produce the same\n    // results for everyone\n    mode !== 'test' && `.env.local`,\n    `.env.${mode}`,\n    '.env',\n  ].filter(Boolean) as string[];\n\n  return dotenvFiles;\n}\n"], "mappings": ";;;;;;;;AAMA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,cAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,aAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,GAAA;EAAA,MAAAN,IAAA,GAAAI,uBAAA,CAAAF,OAAA;EAAAI,EAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,KAAA;EAAA,MAAAR,IAAA,GAAAI,uBAAA,CAAAF,OAAA;EAAAM,IAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6B,SAAAS,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAN,wBAAAU,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAnB,uBAAAa,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAX7B;AACA;AACA;AACA;AACA;AACA;;AAaA,MAAMiB,KAAK,GAAG7B,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,CAAuB;AAEzD,SAAS8B,SAASA,CAAA,EAAY;EACnC,OAAO,CAAC,IAAAC,iBAAO,EAAC,gBAAgB,EAAE,KAAK,CAAC;AAC1C;AAEO,SAASC,2BAA2BA,CAAA,EAAG;EAC5C,MAAMC,QAAQ,GAAGjC,OAAO,CAAC,OAAO,CAAC,CAACkC,OAAO,CAAC,UAAU,CAAC;EAErD,IAAIC,sBAAqD,GAAGC,SAAS;EACrE,IAAIC,IAA6D,GAAGD,SAAS;EAE7E,SAASE,SAASA,CAChBC,WAAmB,EACnBC,OAAoB,GAAG,CAAC,CAAC,EACqC;IAC9D,IAAI,CAACV,SAAS,EAAE,EAAE;MAChBD,KAAK,CAAE,uDAAsD,CAAC;MAC9D,OAAO;QAAEY,GAAG,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC;IAC/B;IAEA,IAAI,CAACP,sBAAsB,EAAE;MAC3BA,sBAAsB,GAAG;QAAE,GAAGQ,OAAO,CAACF;MAAI,CAAC;IAC7C;;IAEA;IACA,MAAMG,WAAW,GAAGC,QAAQ,CAACF,OAAO,CAACF,GAAG,CAACK,QAAQ,EAAEN,OAAO,CAAC;IAE3D,MAAMO,cAAwB,GAAG,EAAE;IACnC,MAAMC,MAAgC,GAAG,CAAC,CAAC;;IAE3C;IACA;IACA;IACA;IACA;IACAJ,WAAW,CAACK,OAAO,CAAEC,UAAU,IAAK;MAClC,MAAMC,kBAAkB,GAAG7C,IAAI,GAAC8C,OAAO,CAACb,WAAW,EAAEW,UAAU,CAAC;MAChE,IAAI,CAAC9C,EAAE,GAACiD,UAAU,CAACF,kBAAkB,CAAC,EAAE;QACtC;MACF;MACA,IAAI;QACF,MAAMG,OAAO,GAAG,IAAAC,sBAAM,EACpBtD,MAAM,GAACuD,MAAM,CAAC;UACZ3B,KAAK,EAAEI,QAAQ;UACf3B,IAAI,EAAE6C,kBAAkB;UACxB;UACAM,QAAQ,EAAE;QACZ,CAAC,CAAC,CACH;QACD,IAAIH,OAAO,CAACN,MAAM,EAAE;UAClBD,cAAc,CAACW,IAAI,CAACP,kBAAkB,CAAC;UACvCtB,KAAK,CAAE,sCAAqCsB,kBAAmB,EAAC,CAAC;UAEjE,KAAK,MAAM5B,GAAG,IAAIH,MAAM,CAACuC,IAAI,CAACL,OAAO,CAACN,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;YAAA,IAAAY,qBAAA;YACnD,IACE,OAAOZ,MAAM,CAACzB,GAAG,CAAC,KAAK,WAAW;YAClC;YACA;YACA,SAAAqC,qBAAA,GAAOzB,sBAAsB,cAAAyB,qBAAA,uBAAtBA,qBAAA,CAAyBrC,GAAG,CAAC,MAAK,WAAW,EACpD;cACAyB,MAAM,CAACzB,GAAG,CAAC,GAAG+B,OAAO,CAACN,MAAM,CAACzB,GAAG,CAAC;YACnC;UACF;QACF,CAAC,MAAM;UACLM,KAAK,CAAE,8CAA6CsB,kBAAmB,EAAC,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOU,KAAc,EAAE;QACvB,IAAIA,KAAK,YAAYC,KAAK,EAAE;UAC1BC,OAAO,CAACF,KAAK,CACV,6CAA4CV,kBAAmB,KAAIU,KAAK,CAACG,OAAQ,EAAC,CACpF;QACH,CAAC,MAAM;UACL,MAAMH,KAAK;QACb;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACd,cAAc,CAACkB,MAAM,EAAE;MAC1BpC,KAAK,CAAE,kDAAiD,CAAC;IAC3D;IAEA,OAAO;MAAEY,GAAG,EAAEO,MAAM;MAAEN,KAAK,EAAEK;IAAe,CAAC;EAC/C;;EAEA;EACA,SAAS9B,GAAGA,CACVsB,WAAmB,EACnBC,OAAoB,GAAG,CAAC,CAAC,EACqC;IAC9D,IAAI,CAACV,SAAS,EAAE,EAAE;MAChBD,KAAK,CAAE,uDAAsD,CAAC;MAC9D,OAAO;QAAEY,GAAG,EAAE,CAAC,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC;IAC/B;IACA,IAAI,CAACF,OAAO,CAAC0B,KAAK,IAAI7B,IAAI,EAAE;MAC1B,OAAOA,IAAI;IACb;IACAA,IAAI,GAAGC,SAAS,CAACC,WAAW,EAAEC,OAAO,CAAC;IACtC,OAAOH,IAAI;EACb;;EAEA;EACA,SAAS8B,IAAIA,CAAC5B,WAAmB,EAAEC,OAAoB,GAAG,CAAC,CAAC,EAAE;IAC5D,IAAI,CAACV,SAAS,EAAE,EAAE;MAChBD,KAAK,CAAE,uDAAsD,CAAC;MAC9D,OAAOc,OAAO,CAACF,GAAG;IACpB;IAEA,MAAM2B,OAAO,GAAGnD,GAAG,CAACsB,WAAW,EAAEC,OAAO,CAAC;IAEzC,IAAI,CAACA,OAAO,CAAC0B,KAAK,EAAE;MAClB,MAAMP,IAAI,GAAGvC,MAAM,CAACuC,IAAI,CAACS,OAAO,CAAC3B,GAAG,CAAC;MACrC,IAAIkB,IAAI,CAACM,MAAM,EAAE;QACfF,OAAO,CAACM,GAAG,CACTC,gBAAK,CAACC,IAAI,CAAC,WAAW,EAAEH,OAAO,CAAC1B,KAAK,CAAC8B,GAAG,CAAEC,IAAI,IAAKnE,IAAI,GAACoE,QAAQ,CAACD,IAAI,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CACpF;QACDZ,OAAO,CAACM,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC,aAAa,EAAEZ,IAAI,CAACgB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MACxD;IACF;IAEAhC,OAAO,CAACF,GAAG,GAAG;MAAE,GAAGE,OAAO,CAACF,GAAG;MAAE,GAAG2B,OAAO,CAAC3B;IAAI,CAAC;IAChD,OAAOE,OAAO,CAACF,GAAG;EACpB;EAEA,OAAO;IACL0B,IAAI;IACJlD,GAAG;IACHqB;EACF,CAAC;AACH;AAEO,SAASO,QAAQA,CACtB+B,IAAwB,EACxB;EAAEC,MAAM,GAAG;AAAmC,CAAC,GAAG,CAAC,CAAC,EAC1C;EACV,IAAI,CAAC/C,SAAS,EAAE,EAAE;IAChBD,KAAK,CAAE,uDAAsD,CAAC;IAC9D,OAAO,EAAE;EACX;EAEA,IAAI,CAAC+C,IAAI,EAAE;IACT,IAAIC,MAAM,EAAE;MACVhD,KAAK,CAAC,gEAAgE,CAAC;IACzE,CAAC,MAAM;MACLkC,OAAO,CAACF,KAAK,CACXS,gBAAK,CAACQ,GAAG,CACP,sIAAsI,CACvI,CACF;MACDf,OAAO,CAACF,KAAK,CAACS,gBAAK,CAACQ,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACnE;EACF;EAEA,IAAIF,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC,CAACG,QAAQ,CAACH,IAAI,CAAC,EAAE;IACjE,MAAM,IAAId,KAAK,CACZ,kCAAiCc,IAAK,uEAAsE,CAC9G;EACH;EAEA,IAAI,CAACA,IAAI,EAAE;IACT;IACA,OAAO,CAAE,YAAW,EAAE,MAAM,CAAC;EAC/B;EACA;EACA,MAAMhC,WAAW,GAAG,CACjB,QAAOgC,IAAK,QAAO;EACpB;EACA;EACA;EACAA,IAAI,KAAK,MAAM,IAAK,YAAW,EAC9B,QAAOA,IAAK,EAAC,EACd,MAAM,CACP,CAACI,MAAM,CAACC,OAAO,CAAa;EAE7B,OAAOrC,WAAW;AACpB"}