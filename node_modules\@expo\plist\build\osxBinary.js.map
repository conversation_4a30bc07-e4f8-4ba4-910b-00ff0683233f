{"version": 3, "file": "osxBinary.js", "sourceRoot": "", "sources": ["../src/osxBinary.ts"], "names": [], "mappings": ";;;;;;AAAA,mDAAmD;AACnD,4CAAoB;AAIpB,MAAM,KAAK,GAAG,KAAK,CAAC;AAEP,QAAA,aAAa,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,SAAS;AAC5C,QAAA,cAAc,GAAG,KAAK,CAAC;AAEpC,oFAAoF;AACpF,uFAAuF;AACvF,yCAAyC;AACzC,MAAM,KAAK,GAAG,YAAY,CAAC;AAE3B,wBAAwB;AAExB,MAAa,GAAG;IACd,YAAmB,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;IAAG,CAAC;CACnC;AAFD,kBAEC;AAEM,KAAK,UAAU,SAAS,CAAC,gBAAiC;IAC/D,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACrC,OAAO,mBAAW,CAAC,gBAAgB,CAAC,CAAC;KACtC;IAED,MAAM,IAAI,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAC1D,OAAO,mBAAW,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAPD,8BAOC;AAAA,CAAC;AAEK,MAAM,WAAW,GAAG,CAAC,MAAc,EAAE,EAAE;IAC5C,eAAe;IACf,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACjE,IAAI,MAAM,KAAK,QAAQ,EAAE;QACvB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACzE;IAED,4CAA4C;IAC5C,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IAChE,8BAA8B;IAC9B,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC;KAC1C;IACD,MAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,aAAa,CAAC,CAAC;KAChD;IACD,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC5C,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC;KAC1C;IACD,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAC5C,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC;KACxC;IACD,MAAM,iBAAiB,GAAG,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACpD,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,iBAAiB,CAAC,CAAC;KACxD;IAED,IAAI,UAAU,GAAG,OAAO,CAAC,cAAc,EAAE;QACvC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C;IAED,sBAAsB;IACtB,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAC9B,iBAAiB,GAAG,CAAC,GAAG,UAAU,EAClC,iBAAiB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CACzC,CAAC;QACF,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,GAAG,CACT,qBAAqB;gBACnB,CAAC;gBACD,MAAM;gBACN,WAAW,CAAC,CAAC,CAAC;gBACd,IAAI;gBACJ,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3B,GAAG,CACN,CAAC;SACH;KACF;IAED,qEAAqE;IACrE,qCAAqC;IACrC,+EAA+E;IAC/E,0DAA0D;IAC1D,SAAS,WAAW,CAAC,WAAmB;QACtC,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5B,MAAM,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;QACnD,MAAM,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,eAAe;QAC5C,QAAQ,OAAO,EAAE;YACf,KAAK,GAAG;gBACN,OAAO,WAAW,EAAE,CAAC;YACvB,KAAK,GAAG;gBACN,OAAO,YAAY,EAAE,CAAC;YACxB,KAAK,GAAG;gBACN,OAAO,QAAQ,EAAE,CAAC;YACpB,KAAK,GAAG;gBACN,OAAO,SAAS,EAAE,CAAC;YACrB,KAAK,GAAG;gBACN,OAAO,SAAS,EAAE,CAAC;YACrB,KAAK,GAAG;gBACN,OAAO,SAAS,EAAE,CAAC;YACrB,KAAK,GAAG,EAAE,QAAQ;gBAChB,OAAO,gBAAgB,EAAE,CAAC;YAC5B,KAAK,GAAG,EAAE,SAAS;gBACjB,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAChC,KAAK,GAAG;gBACN,OAAO,UAAU,EAAE,CAAC;YACtB,KAAK,GAAG;gBACN,OAAO,eAAe,EAAE,CAAC;YAC3B;gBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;SAC/D;QAED,SAAS,WAAW;YAClB,QAAQ;YACR,QAAQ,OAAO,EAAE;gBACf,KAAK,GAAG,EAAE,OAAO;oBACf,OAAO,IAAI,CAAC;gBACd,KAAK,GAAG,EAAE,QAAQ;oBAChB,OAAO,KAAK,CAAC;gBACf,KAAK,GAAG,EAAE,OAAO;oBACf,OAAO,IAAI,CAAC;gBACd,KAAK,GAAG,EAAE,cAAc;oBACtB,OAAO,IAAI,CAAC;gBACd;oBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;aACtE;QACH,CAAC;QAED,SAAS,iBAAiB,CAAC,MAAc;YACvC,IAAI,GAAG,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,CAAC;YACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;oBACrB,MAAM;iBACP;aACF;YACD,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7B,MAAM,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC3C,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACrC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,SAAS,YAAY;YACnB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACpC,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE;gBAClC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC3D,IAAI,MAAM,KAAK,EAAE,EAAE;oBACjB,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACpC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,CAAC,mBAAmB;iBACvC;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;oBAC/B,GAAG,KAAK,CAAC,CAAC;oBACV,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC;oBAClB,OAAO,GAAG,CAAC;gBACb,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,IAAI,KAAK,CACb,kDAAkD;oBAChD,MAAM;oBACN,mBAAmB;oBACnB,OAAO,CAAC,aAAa;oBACrB,iBAAiB,CACpB,CAAC;aACH;QACH,CAAC;QAED,SAAS,QAAQ;YACf,MAAM,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC;YAC3B,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE;gBAClC,OAAO,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;aACzE;YACD,MAAM,IAAI,KAAK,CACb,kDAAkD;gBAChD,MAAM;gBACN,mBAAmB;gBACnB,OAAO,CAAC,aAAa;gBACrB,iBAAiB,CACpB,CAAC;QACJ,CAAC;QAED,SAAS,SAAS;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACpC,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE;gBAClC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;gBACjE,IAAI,MAAM,KAAK,CAAC,EAAE;oBAChB,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;iBAClC;gBACD,IAAI,MAAM,KAAK,CAAC,EAAE;oBAChB,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;iBACnC;aACF;iBAAM;gBACL,MAAM,IAAI,KAAK,CACb,kDAAkD;oBAChD,MAAM;oBACN,mBAAmB;oBACnB,OAAO,CAAC,aAAa;oBACrB,iBAAiB,CACpB,CAAC;aACH;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,SAAS;YAChB,IAAI,OAAO,IAAI,GAAG,EAAE;gBAClB,OAAO,CAAC,KAAK,CAAC,qBAAqB,GAAG,OAAO,GAAG,qBAAqB,CAAC,CAAC;aACxE;YACD,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;YACxD,OAAO,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,SAAS,SAAS;YAChB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,MAAM,GAAG,OAAO,CAAC;YACrB,IAAI,OAAO,IAAI,GAAG,EAAE;gBAClB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM,OAAO,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACzC,IAAI,OAAO,IAAI,GAAG,EAAE;oBAClB,OAAO,CAAC,KAAK,CAAC,mCAAmC,GAAG,OAAO,CAAC,CAAC;iBAC9D;gBACD,MAAM,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvC,UAAU,GAAG,CAAC,GAAG,SAAS,CAAC;gBAC3B,IAAI,SAAS,GAAG,CAAC,EAAE;oBACjB,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;qBAAM;oBACL,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;aACF;YACD,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE;gBAClC,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC;aACxE;YACD,MAAM,IAAI,KAAK,CACb,kDAAkD;gBAChD,MAAM;gBACN,mBAAmB;gBACnB,OAAO,CAAC,aAAa;gBACrB,iBAAiB,CACpB,CAAC;QACJ,CAAC;QAED,SAAS,gBAAgB,CAAC,OAAgB;YACxC,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;YACvB,IAAI,GAAG,GAAG,MAAM,CAAC;YACjB,IAAI,MAAM,GAAG,OAAO,CAAC;YACrB,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,OAAO,IAAI,GAAG,EAAE;gBAClB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM,OAAO,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACzC,IAAI,OAAO,IAAI,GAAG,EAAE;oBAClB,OAAO,CAAC,KAAK,CAAC,8BAA8B,GAAG,OAAO,CAAC,CAAC;iBACzD;gBACD,MAAM,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC;gBAC1B,IAAI,SAAS,GAAG,CAAC,EAAE;oBACjB,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;qBAAM;oBACL,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;aACF;YACD,sGAAsG;YACtG,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC;YACtB,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE;gBAClC,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC,CAC9D,CAAC;gBACF,IAAI,OAAO,EAAE;oBACX,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;oBACrC,GAAG,GAAG,MAAM,CAAC;iBACd;gBACD,OAAO,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,MAAM,IAAI,KAAK,CACb,kDAAkD;gBAChD,MAAM;gBACN,mBAAmB;gBACnB,OAAO,CAAC,aAAa;gBACrB,iBAAiB,CACpB,CAAC;QACJ,CAAC;QAED,SAAS,UAAU;YACjB,IAAI,MAAM,GAAG,OAAO,CAAC;YACrB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,OAAO,IAAI,GAAG,EAAE;gBAClB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM,OAAO,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACzC,IAAI,OAAO,IAAI,GAAG,EAAE;oBAClB,OAAO,CAAC,KAAK,CAAC,mCAAmC,GAAG,OAAO,CAAC,CAAC;iBAC9D;gBACD,MAAM,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvC,WAAW,GAAG,CAAC,GAAG,SAAS,CAAC;gBAC5B,IAAI,SAAS,GAAG,CAAC,EAAE;oBACjB,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;qBAAM;oBACL,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;aACF;YACD,IAAI,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE;gBAClD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;aACrD;YACD,MAAM,KAAK,GAAgC,EAAE,CAAC;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,MAAM,MAAM,GAAG,QAAQ,CACrB,MAAM,CAAC,KAAK,CACV,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,aAAa,EACxC,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CAC/C,CACF,CAAC;gBACF,KAAK,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;aAChC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe;YACtB,IAAI,MAAM,GAAG,OAAO,CAAC;YACrB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,OAAO,IAAI,GAAG,EAAE;gBAClB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM,OAAO,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACzC,IAAI,OAAO,IAAI,GAAG,EAAE;oBAClB,OAAO,CAAC,KAAK,CAAC,mCAAmC,GAAG,OAAO,CAAC,CAAC;iBAC9D;gBACD,MAAM,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvC,UAAU,GAAG,CAAC,GAAG,SAAS,CAAC;gBAC3B,IAAI,SAAS,GAAG,CAAC,EAAE;oBACjB,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;qBAAM;oBACL,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBACrE;aACF;YACD,IAAI,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE;gBACtD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;aACrD;YACD,IAAI,KAAK,EAAE;gBACT,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,WAAW,CAAC,CAAC;aACnD;YACD,MAAM,IAAI,GAA4C,EAAE,CAAC;YACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,MAAM,MAAM,GAAG,QAAQ,CACrB,MAAM,CAAC,KAAK,CACV,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,EACvC,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CAC9C,CACF,CAAC;gBACF,MAAM,MAAM,GAAG,QAAQ,CACrB,MAAM,CAAC,KAAK,CACV,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa,EAChE,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CACvE,CACF,CAAC;gBACF,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;gBAChC,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,KAAK,EAAE;oBACT,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,WAAW,GAAG,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC;iBAC1E;gBACD,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;aACjB;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;AAClC,CAAE,CAAC;AAzVU,QAAA,WAAW,eAyVrB;AAEH,SAAS,QAAQ,CAAC,MAAc,EAAE,QAAgB,CAAC;IACjD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;IAEnB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,CAAC,KAAK,CAAC,CAAC;QACR,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACvB;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,2FAA2F;AAC3F,SAAS,YAAY,CAAC,MAAc,EAAE,KAAa;IACjD,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,SAAS,CAAC,MAAc;IAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QAC/B,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACpB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KACnB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// adapted from https://github.com/3breadt/dd-plist\nimport fs from 'fs';\n\nimport { PlistValue } from '.';\n\nconst debug = false;\n\nexport const maxObjectSize = 100 * 1000 * 1000; // 100Meg\nexport const maxObjectCount = 32768;\n\n// EPOCH = new SimpleDateFormat(\"yyyy MM dd zzz\").parse(\"2001 01 01 GMT\").getTime();\n// ...but that's annoying in a static initializer because it can throw exceptions, ick.\n// So we just hardcode the correct value.\nconst EPOCH = ************;\n\n// UID object definition\n\nexport class UID {\n  constructor(public UID: number) {}\n}\n\nexport async function parseFile(fileNameOrBuffer: string | Buffer) {\n  if (Buffer.isBuffer(fileNameOrBuffer)) {\n    return parseBuffer(fileNameOrBuffer);\n  }\n\n  const data = await fs.promises.readFile(fileNameOrBuffer);\n  return parseBuffer(data);\n};\n\nexport const parseBuffer = (buffer: Buffer) => {\n  // check header\n  const header = buffer.slice(0, 'bplist'.length).toString('utf8');\n  if (header !== 'bplist') {\n    throw new Error(\"Invalid binary plist. Expected 'bplist' at offset 0.\");\n  }\n\n  // Handle trailer, last 32 bytes of the file\n  const trailer = buffer.slice(buffer.length - 32, buffer.length);\n  // 6 null bytes (index 0 to 5)\n  const offsetSize = trailer.readUInt8(6);\n  if (debug) {\n    console.log('offsetSize: ' + offsetSize);\n  }\n  const objectRefSize = trailer.readUInt8(7);\n  if (debug) {\n    console.log('objectRefSize: ' + objectRefSize);\n  }\n  const numObjects = readUInt64BE(trailer, 8);\n  if (debug) {\n    console.log('numObjects: ' + numObjects);\n  }\n  const topObject = readUInt64BE(trailer, 16);\n  if (debug) {\n    console.log('topObject: ' + topObject);\n  }\n  const offsetTableOffset = readUInt64BE(trailer, 24);\n  if (debug) {\n    console.log('offsetTableOffset: ' + offsetTableOffset);\n  }\n\n  if (numObjects > exports.maxObjectCount) {\n    throw new Error('maxObjectCount exceeded');\n  }\n\n  // Handle offset table\n  const offsetTable: number[] = [];\n\n  for (let i = 0; i < numObjects; i++) {\n    const offsetBytes = buffer.slice(\n      offsetTableOffset + i * offsetSize,\n      offsetTableOffset + (i + 1) * offsetSize\n    );\n    offsetTable[i] = readUInt(offsetBytes, 0);\n    if (debug) {\n      console.log(\n        'Offset for Object #' +\n          i +\n          ' is ' +\n          offsetTable[i] +\n          ' [' +\n          offsetTable[i].toString(16) +\n          ']'\n      );\n    }\n  }\n\n  // Parses an object inside the currently parsed binary property list.\n  // For the format specification check\n  // <a href=\"https://www.opensource.apple.com/source/CF/CF-635/CFBinaryPList.c\">\n  // Apple's binary property list parser implementation</a>.\n  function parseObject(tableOffset: number): PlistValue | null | UID {\n    const offset = offsetTable[tableOffset];\n    const type = buffer[offset];\n    const objType = (type & 0xf0) >> 4; //First  4 bits\n    const objInfo = type & 0x0f; //Second 4 bits\n    switch (objType) {\n      case 0x0:\n        return parseSimple();\n      case 0x1:\n        return parseInteger();\n      case 0x8:\n        return parseUID();\n      case 0x2:\n        return parseReal();\n      case 0x3:\n        return parseDate();\n      case 0x4:\n        return parseData();\n      case 0x5: // ASCII\n        return parsePlistString();\n      case 0x6: // UTF-16\n        return parsePlistString(true);\n      case 0xa:\n        return parseArray();\n      case 0xd:\n        return parseDictionary();\n      default:\n        throw new Error('Unhandled type 0x' + objType.toString(16));\n    }\n\n    function parseSimple(): boolean | null {\n      //Simple\n      switch (objInfo) {\n        case 0x0: // null\n          return null;\n        case 0x8: // false\n          return false;\n        case 0x9: // true\n          return true;\n        case 0xf: // filler byte\n          return null;\n        default:\n          throw new Error('Unhandled simple type 0x' + objType.toString(16));\n      }\n    }\n\n    function bufferToHexString(buffer: Buffer) {\n      let str = '';\n      let i;\n      for (i = 0; i < buffer.length; i++) {\n        if (buffer[i] != 0x00) {\n          break;\n        }\n      }\n      for (; i < buffer.length; i++) {\n        const part = '00' + buffer[i].toString(16);\n        str += part.substr(part.length - 2);\n      }\n      return str;\n    }\n\n    function parseInteger(): number | BigInt {\n      const length = Math.pow(2, objInfo);\n      if (length < exports.maxObjectSize) {\n        const data = buffer.slice(offset + 1, offset + 1 + length);\n        if (length === 16) {\n          const str = bufferToHexString(data);\n          return BigInt(str) // bigInt(str, 16);\n        }\n        return data.reduce((acc, curr) => {\n          acc <<= 8;\n          acc |= curr & 255;\n          return acc;\n        });\n      } else {\n        throw new Error(\n          'Too little heap space available! Wanted to read ' +\n            length +\n            ' bytes, but only ' +\n            exports.maxObjectSize +\n            ' are available.'\n        );\n      }\n    }\n\n    function parseUID() {\n      const length = objInfo + 1;\n      if (length < exports.maxObjectSize) {\n        return new UID(readUInt(buffer.slice(offset + 1, offset + 1 + length)));\n      }\n      throw new Error(\n        'Too little heap space available! Wanted to read ' +\n          length +\n          ' bytes, but only ' +\n          exports.maxObjectSize +\n          ' are available.'\n      );\n    }\n\n    function parseReal(): number | null {\n      const length = Math.pow(2, objInfo);\n      if (length < exports.maxObjectSize) {\n        const realBuffer = buffer.slice(offset + 1, offset + 1 + length);\n        if (length === 4) {\n          return realBuffer.readFloatBE(0);\n        }\n        if (length === 8) {\n          return realBuffer.readDoubleBE(0);\n        }\n      } else {\n        throw new Error(\n          'Too little heap space available! Wanted to read ' +\n            length +\n            ' bytes, but only ' +\n            exports.maxObjectSize +\n            ' are available.'\n        );\n      }\n      return null;\n    }\n\n    function parseDate() {\n      if (objInfo != 0x3) {\n        console.error('Unknown date type :' + objInfo + '. Parsing anyway...');\n      }\n      const dateBuffer = buffer.slice(offset + 1, offset + 9);\n      return new Date(EPOCH + 1000 * dateBuffer.readDoubleBE(0));\n    }\n\n    function parseData() {\n      let dataoffset = 1;\n      let length = objInfo;\n      if (objInfo == 0xf) {\n        const int_type = buffer[offset + 1];\n        const intType = (int_type & 0xf0) / 0x10;\n        if (intType != 0x1) {\n          console.error('0x4: UNEXPECTED LENGTH-INT TYPE! ' + intType);\n        }\n        const intInfo = int_type & 0x0f;\n        const intLength = Math.pow(2, intInfo);\n        dataoffset = 2 + intLength;\n        if (intLength < 3) {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        } else {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        }\n      }\n      if (length < exports.maxObjectSize) {\n        return buffer.slice(offset + dataoffset, offset + dataoffset + length);\n      }\n      throw new Error(\n        'Too little heap space available! Wanted to read ' +\n          length +\n          ' bytes, but only ' +\n          exports.maxObjectSize +\n          ' are available.'\n      );\n    }\n\n    function parsePlistString(isUtf16?: number): string {\n      isUtf16 = isUtf16 || 0;\n      let enc = 'utf8';\n      let length = objInfo;\n      let stroffset = 1;\n      if (objInfo == 0xf) {\n        const int_type = buffer[offset + 1];\n        const intType = (int_type & 0xf0) / 0x10;\n        if (intType != 0x1) {\n          console.error('UNEXPECTED LENGTH-INT TYPE! ' + intType);\n        }\n        const intInfo = int_type & 0x0f;\n        const intLength = Math.pow(2, intInfo);\n        stroffset = 2 + intLength;\n        if (intLength < 3) {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        } else {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        }\n      }\n      // length is String length -> to get byte length multiply by 2, as 1 character takes 2 bytes in UTF-16\n      length *= isUtf16 + 1;\n      if (length < exports.maxObjectSize) {\n        let plistString = Buffer.from(\n          buffer.slice(offset + stroffset, offset + stroffset + length)\n        );\n        if (isUtf16) {\n          plistString = swapBytes(plistString);\n          enc = 'ucs2';\n        }\n        return plistString.toString(enc);\n      }\n      throw new Error(\n        'Too little heap space available! Wanted to read ' +\n          length +\n          ' bytes, but only ' +\n          exports.maxObjectSize +\n          ' are available.'\n      );\n    }\n\n    function parseArray() {\n      let length = objInfo;\n      let arrayoffset = 1;\n      if (objInfo == 0xf) {\n        const int_type = buffer[offset + 1];\n        const intType = (int_type & 0xf0) / 0x10;\n        if (intType != 0x1) {\n          console.error('0xa: UNEXPECTED LENGTH-INT TYPE! ' + intType);\n        }\n        const intInfo = int_type & 0x0f;\n        const intLength = Math.pow(2, intInfo);\n        arrayoffset = 2 + intLength;\n        if (intLength < 3) {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        } else {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        }\n      }\n      if (length * objectRefSize > exports.maxObjectSize) {\n        throw new Error('Too little heap space available!');\n      }\n      const array: (PlistValue | UID | null)[] = [];\n      for (let i = 0; i < length; i++) {\n        const objRef = readUInt(\n          buffer.slice(\n            offset + arrayoffset + i * objectRefSize,\n            offset + arrayoffset + (i + 1) * objectRefSize\n          )\n        );\n        array[i] = parseObject(objRef);\n      }\n      return array;\n    }\n\n    function parseDictionary() {\n      let length = objInfo;\n      let dictoffset = 1;\n      if (objInfo == 0xf) {\n        const int_type = buffer[offset + 1];\n        const intType = (int_type & 0xf0) / 0x10;\n        if (intType != 0x1) {\n          console.error('0xD: UNEXPECTED LENGTH-INT TYPE! ' + intType);\n        }\n        const intInfo = int_type & 0x0f;\n        const intLength = Math.pow(2, intInfo);\n        dictoffset = 2 + intLength;\n        if (intLength < 3) {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        } else {\n          length = readUInt(buffer.slice(offset + 2, offset + 2 + intLength));\n        }\n      }\n      if (length * 2 * objectRefSize > exports.maxObjectSize) {\n        throw new Error('Too little heap space available!');\n      }\n      if (debug) {\n        console.log('Parsing dictionary #' + tableOffset);\n      }\n      const dict: Record<string, PlistValue | UID | null> = {};\n      for (let i = 0; i < length; i++) {\n        const keyRef = readUInt(\n          buffer.slice(\n            offset + dictoffset + i * objectRefSize,\n            offset + dictoffset + (i + 1) * objectRefSize\n          )\n        );\n        const valRef = readUInt(\n          buffer.slice(\n            offset + dictoffset + length * objectRefSize + i * objectRefSize,\n            offset + dictoffset + length * objectRefSize + (i + 1) * objectRefSize\n          )\n        );\n        const key = parseObject(keyRef);\n        const val = parseObject(valRef);\n        if (debug) {\n          console.log('  DICT #' + tableOffset + ': Mapped ' + key + ' to ' + val);\n        }\n        dict[key] = val;\n      }\n      return dict;\n    }\n  }\n\n  return [parseObject(topObject)];\n});\n\nfunction readUInt(buffer: Buffer, start: number = 0): number {\n  start = start || 0;\n\n  let l = 0;\n  for (let i = start; i < buffer.length; i++) {\n    l <<= 8;\n    l |= buffer[i] & 0xff;\n  }\n  return l;\n}\n\n// we're just going to toss the high order bits because javascript doesn't have 64-bit ints\nfunction readUInt64BE(buffer: Buffer, start: number) {\n  const data = buffer.slice(start, start + 8);\n  return data.readUInt32BE(4, 8);\n}\n\nfunction swapBytes(buffer: Buffer) {\n  const len = buffer.length;\n  for (let i = 0; i < len; i += 2) {\n    const a = buffer[i];\n    buffer[i] = buffer[i + 1];\n    buffer[i + 1] = a;\n  }\n  return buffer;\n}\n"]}