{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ScreenContext", "NativeScreen", "InnerScreen", "_reactNative", "require", "_react", "_interopRequireDefault", "_core", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "View", "React", "Component", "render", "active", "activityState", "style", "enabled", "screensEnabled", "rest", "props", "undefined", "createElement", "hidden", "display", "Screen", "Animated", "createAnimatedComponent", "createContext", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.web.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,YAAA,GAAAJ,OAAA,CAAAK,WAAA;AAGb,IAAAC,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAH,OAAA;AAAyC,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAT,OAAA,EAAAS,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAAf,MAAA,CAAAgB,MAAA,GAAAhB,MAAA,CAAAgB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA;AAElC,MAAMZ,WAAW,GAAAL,OAAA,CAAAK,WAAA,GAAGmB,iBAAI;;AAE/B;AACA;AACA;AACO,MAAMpB,YAAY,SAASqB,cAAK,CAACC,SAAS,CAAc;EAC7DC,MAAMA,CAAA,EAAgB;IACpB,IAAI;MACFC,MAAM;MACNC,aAAa;MACbC,KAAK;MACLC,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;MAC1B,GAAGC;IACL,CAAC,GAAG,IAAI,CAACC,KAAK;IAEd,IAAIH,OAAO,EAAE;MACX,IAAIH,MAAM,KAAKO,SAAS,IAAIN,aAAa,KAAKM,SAAS,EAAE;QACvDN,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;MACA,oBACEpB,MAAA,CAAAN,OAAA,CAAAkC,aAAA,CAAC9B,YAAA,CAAAkB;MACC;MAAA,EAAAX,QAAA;QACAwB,MAAM,EAAER,aAAa,KAAK,CAAE;QAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEQ,OAAO,EAAET,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC;MAAE,GAC/DI,IAAI,CACT,CAAC;IAEN;IAEA,oBAAOzB,MAAA,CAAAN,OAAA,CAAAkC,aAAA,CAAC9B,YAAA,CAAAkB,IAAI,EAAKS,IAAO,CAAC;EAC3B;AACF;AAACjC,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAED,MAAMmC,MAAM,GAAGC,qBAAQ,CAACC,uBAAuB,CAACrC,YAAY,CAAC;AAEtD,MAAMD,aAAa,GAAAH,OAAA,CAAAG,aAAA,gBAAGsB,cAAK,CAACiB,aAAa,CAACH,MAAM,CAAC;AAAC,IAAAI,QAAA,GAAA3C,OAAA,CAAAE,OAAA,GAE1CqC,MAAM", "ignoreList": []}