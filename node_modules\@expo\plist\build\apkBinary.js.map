{"version": 3, "file": "apkBinary.js", "sourceRoot": "", "sources": ["../src/apkBinary.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;GAEG;AACH,+CAAiC;AAEjC,6BAA6B;AAC7B,oEAAoE;AAEpE,MAAM,QAAQ,GAAG;IACf,YAAY,EAAE,CAAC;IACf,cAAc,EAAE,CAAC;IACjB,kBAAkB,EAAE,CAAC;CACtB,CAAC;AAEF,MAAM,SAAS,GAAG;IAChB,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,MAAM;IACnB,KAAK,EAAE,MAAM;IACb,GAAG,EAAE,MAAM;IACX,eAAe,EAAE,MAAM;IACvB,mBAAmB,EAAE,MAAM;IAC3B,iBAAiB,EAAE,MAAM;IACzB,iBAAiB,EAAE,MAAM;IACzB,eAAe,EAAE,MAAM;IACvB,SAAS,EAAE,MAAM;IACjB,cAAc,EAAE,MAAM;IACtB,gBAAgB,EAAE,MAAM;IACxB,aAAa,EAAE,MAAM;IACrB,UAAU,EAAE,MAAM;IAClB,eAAe,EAAE,MAAM;CACxB,CAAC;AAEF,MAAM,WAAW,GAAG;IAClB,MAAM,EAAE,CAAC,IAAI,CAAC;IACd,IAAI,EAAE,CAAC,IAAI,CAAC;CACb,CAAC;AAEF,qCAAqC;AACrC,MAAM,UAAU,GAAG;IACjB,qBAAqB,EAAE,UAAU;IACjC,sBAAsB,EAAE,UAAU;IAClC,kBAAkB,EAAE,UAAU;IAC9B,kBAAkB,EAAE,UAAU;IAC9B,kBAAkB,EAAE,UAAU;IAC9B,kBAAkB,EAAE,UAAU;IAC9B,kBAAkB,EAAE,UAAU;IAC9B,mBAAmB,EAAE,UAAU;IAC/B,gBAAgB,EAAE,UAAU;IAC5B,qBAAqB,EAAE,UAAU;IACjC,4BAA4B,EAAE,UAAU;IACxC,eAAe,EAAE,UAAU;IAC3B,iBAAiB,EAAE,UAAU;IAC7B,eAAe,EAAE,UAAU;IAC3B,eAAe,EAAE,UAAU;IAC3B,eAAe,EAAE,UAAU;IAC3B,kBAAkB,EAAE,UAAU;IAC9B,eAAe,EAAE,UAAU;IAC3B,eAAe,EAAE,UAAU;IAC3B,YAAY,EAAE,UAAU;IACxB,cAAc,EAAE,UAAU;IAC1B,cAAc,EAAE,UAAU;IAC1B,oBAAoB,EAAE,UAAU;IAChC,cAAc,EAAE,UAAU;IAC1B,UAAU,EAAE,UAAU;IACtB,aAAa,EAAE,UAAU;IACzB,gBAAgB,EAAE,UAAU;IAC5B,oBAAoB,EAAE,UAAU;IAChC,oBAAoB,EAAE,UAAU;IAChC,mBAAmB,EAAE,UAAU;IAC/B,mBAAmB,EAAE,UAAU;IAC/B,YAAY,EAAE,UAAU;IACxB,YAAY,EAAE,UAAU;IACxB,mBAAmB,EAAE,UAAU;IAC/B,aAAa,EAAE,UAAU;IACzB,SAAS,EAAE,UAAU;IACrB,cAAc,EAAE,UAAU;IAC1B,WAAW,EAAE,UAAU;CACxB,CAAC;AAEF,MAAa,eAAe;IAQ1B,YAAmB,MAAc,EAAE,UAAe,EAAE;QAAjC,WAAM,GAAN,MAAM,CAAQ;QAPjC,WAAM,GAAG,CAAC,CAAC;QACX,YAAO,GAAa,EAAE,CAAC;QACvB,cAAS,GAAU,EAAE,CAAC;QAGtB,UAAK,GAAU,EAAE,CAAC;QAClB,UAAK,GAAG,KAAK,CAAC;QAEZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;IACtC,CAAC;IAED,MAAM;QACJ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QACjB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,OAAO;QACL,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QACjB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,OAAO;QACL,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QACjB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,OAAO;QACL,oBAAoB;QACpB,iCAAiC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,wBAAwB;QACxB,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QACjB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,WAAW;QACT,wBAAwB;QACxB,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,GAAG,GAAG,IAAI,EAAE;YACd,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YACxB,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;SACtB;QACD,yBAAyB;QACzB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,YAAY;QACV,yBAAyB;QACzB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,GAAG,GAAG,MAAM,EAAE;YAChB,GAAG,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAC3B,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;SACvB;QACD,yBAAyB;QACzB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,aAAa;QACX,0BAA0B;QAE1B,MAAM,SAAS,GAAQ;YACrB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;QAEpC,SAAS,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QAC7B,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;QAEzB,QAAQ,IAAI,EAAE;YACZ,KAAK,UAAU,CAAC,eAAe;gBAC7B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR,KAAK,UAAU,CAAC,eAAe;gBAC7B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR,KAAK,UAAU,CAAC,gBAAgB;gBAC9B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR,KAAK,UAAU,CAAC,eAAe;gBAC7B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR,KAAK,UAAU,CAAC,eAAe;gBAC7B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR,KAAK,UAAU,CAAC,eAAe;gBAC7B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,MAAM;SACT;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,YAAY;QACV,MAAM,QAAQ,GAAQ;YACpB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC;QAEzB,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;QAExB,QAAQ,IAAI,EAAE;YACZ,KAAK,UAAU,CAAC,qBAAqB;gBACnC,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC;gBACpB,MAAM;YACR,KAAK,UAAU,CAAC,4BAA4B;gBAC1C,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;gBACrB,MAAM;SACT;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,SAAS;QACP,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACrD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,SAAS;QACP,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACxC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,cAAc;QACZ,MAAM,UAAU,GAAQ;YACtB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE/B,oEAAoE;QACpE,IAAI,IAAI,KAAK,CAAC,EAAE;YACd,IAAI,GAAG,CAAC,CAAC;SACV;QAED,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC;QAE9B,QAAQ,QAAQ,EAAE;YAChB,KAAK,UAAU,CAAC,YAAY;gBAC1B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;gBAC5B,MAAM;YACR,KAAK,UAAU,CAAC,YAAY;gBAC1B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;gBAC5B,MAAM;YACR,KAAK,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3B,UAAU,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;gBAC3B,MAAM;aACP;YACD,KAAK,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC9B,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC1B,UAAU,CAAC,KAAK,GAAG,gBAAgB,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACrD,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;gBAC9B,MAAM;aACP;YACD,KAAK,UAAU,CAAC,gBAAgB;gBAC9B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBACxC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;gBAC5B,MAAM;YACR,KAAK,UAAU,CAAC,SAAS;gBACvB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;gBACxB,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC;gBACzB,MAAM;YACR,KAAK,UAAU,CAAC,mBAAmB;gBACjC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpC,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC;gBACzB,MAAM;YACR,KAAK,UAAU,CAAC,mBAAmB;gBACjC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpC,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC;gBACzB,MAAM;YACR,KAAK,UAAU,CAAC,oBAAoB;gBAClC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpC,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC;gBAC1B,MAAM;YACR,KAAK,UAAU,CAAC,oBAAoB;gBAClC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpC,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC;gBAC1B,MAAM;YACR,KAAK,UAAU,CAAC,cAAc;gBAC5B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;gBAC9B,MAAM;YACR,KAAK,UAAU,CAAC,aAAa;gBAC3B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC;gBAC7B,MAAM;YACR,OAAO,CAAC,CAAC;gBACP,sCAAsC;gBACtC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;aAC7B;SACF;QAED,oCAAoC;QACpC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE;YACvB,sCAAsC;YACtC,kCAAkC;YAClC,kFAAkF;YAClF,iFAAiF;YACjF,8EAA8E;YAC9E,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;SACnB;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,yDAAyD;IACzD,iBAAiB,CAAC,GAAW;QAC3B,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAC7B,OAAO,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAED,UAAU,CAAC,QAAgB;QACzB,IAAI,YAAY,CAAC;QACjB,IAAI,UAAU,CAAC;QACf,IAAI,KAAK,CAAC;QACV,QAAQ,QAAQ,EAAE;YAChB,KAAK,OAAO;gBACV,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClC,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAChC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC;gBACjF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,CAAC;gBACrE,OAAO,KAAK,CAAC;YACf,KAAK,MAAM;gBACT,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACnC,UAAU,GAAG,YAAY,GAAG,CAAC,CAAC;gBAC9B,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC;gBACjF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,CAAC;gBACtE,OAAO,KAAK,CAAC;YACf;gBACE,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,yBAAyB,QAAQ,GAAG,EAAE,CAAC,CAAC;SACtF;IACH,CAAC;IAED,eAAe;QAMb,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,MAAM;YACxB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE;YACzB,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE;YAC1B,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE;SAC1B,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,MAAW;QACxB,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9B,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACrC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAEpC,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE;YAC9C,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;SAC5E;QAED,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAClD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9B;QAED,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;QAE3F,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;QAC9D,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAClD,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9C;QAED,cAAc;QACd,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QAEpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe,CAAC,MAAW;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QACrE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SACrC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,qBAAqB,EAAC,YAAY;QAChC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,+BAA+B;QAC/B,qCAAqC;QACrC,oCAAoC;QACpC,iCAAiC;QAEjC,2DAA2D;QAC3D,uBAAuB;QACvB,EAAE;QACF,oEAAoE;QACpE,2DAA2D;QAE3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,EAAC,YAAY;QAC9B,gCAAgC;QAEhC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,+BAA+B;QAC/B,qCAAqC;QACrC,oCAAoC;QACpC,iCAAiC;QAEjC,2DAA2D;QAC3D,uBAAuB;QACvB,EAAE;QACF,oEAAoE;QACpE,2DAA2D;QAE3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,EAAC,YAAY;QAC9B,gCAAgC;QAEhC,MAAM,IAAI,GAAQ;YAChB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,QAAQ,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,EAAE;YACd,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,+BAA+B;QAC/B,qCAAqC;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAE/B,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,oCAAoC;QACpC,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACjC,kCAAkC;QAClC,qCAAqC;QACrC,qCAAqC;QACrC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;SAC/C;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gBAAgB;QACd,6BAA6B;QAE7B,MAAM,IAAI,GAAQ;YAChB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,QAAQ,CAAC,cAAc;YACjC,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,UAAU,EAAE,IAAI;SACjB,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAEhC,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAExC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB,EAAC,YAAY;QAC5B,yBAAyB;QAEzB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,+BAA+B;QAC/B,qCAAqC;QACrC,gCAAgC;QAChC,kCAAkC;QAElC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEhD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,EAAC,YAAY;QACvB,yBAAyB;QAEzB,MAAM,KAAK,GAAQ;YACjB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,QAAQ,CAAC,kBAAkB;YACrC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,IAAI;YACV,UAAU,EAAE,IAAI;SACjB,CAAC;QAEF,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,+BAA+B;QAC/B,qCAAqC;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAE/B,IAAI,OAAO,GAAG,CAAC,EAAE;YACf,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACpC;QAED,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEzC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEnC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,qBAAqB;QACrB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,kBAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACzC,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,GAAG,EAAE;YACzC,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;SACpE;QAED,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACvC,kBAAkB;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACtC,QAAQ,MAAM,CAAC,SAAS,EAAE;gBACxB,KAAK,SAAS,CAAC,WAAW;oBACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;oBAC5B,MAAM;gBACR,KAAK,SAAS,CAAC,gBAAgB;oBAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAC7B,MAAM;gBACR,KAAK,SAAS,CAAC,mBAAmB;oBAChC,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;gBACR,KAAK,SAAS,CAAC,iBAAiB;oBAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,MAAM;gBACR,KAAK,SAAS,CAAC,iBAAiB;oBAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,MAAM;gBACR,KAAK,SAAS,CAAC,eAAe;oBAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;gBACR,KAAK,SAAS,CAAC,SAAS;oBACtB,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,MAAM;gBACR,KAAK,SAAS,CAAC,IAAI;oBACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBACtB,MAAM;gBACR;oBACE,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC;wBAC9B,OAAO,EAAE,2BAA2B,MAAM,CAAC,SAAS,GAAG;qBACxD,CAAC,CAAC;aACN;YAED,oCAAoC;YACpC,MAAM,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC;YACrC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE;gBACvB,kCAAkC;gBAClC,8CAA8C;gBAC9C,wEAAwE;gBACxE,gFAAgF;gBAChF,uEAAuE;gBACvE,6BAA6B;aAC9B;SACF;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAliBD,0CAkiBC", "sourcesContent": ["/**\n * https://github.com/openstf/adbkit-apkreader/blob/368f6b207c57e82fa7373c1608920ca7f4a8904c/lib/apkreader/parser/binaryxml.js\n */\nimport * as assert from 'assert';\n\n// import Debug from 'debug';\n// const debug = Debug('native-run:android:util:binary-xml-parser');\n\nconst NodeType = {\n  ELEMENT_NODE: 1,\n  ATTRIBUTE_NODE: 2,\n  CDATA_SECTION_NODE: 4,\n};\n\nconst ChunkType = {\n  NULL: 0x0000,\n  STRING_POOL: 0x0001,\n  TABLE: 0x0002,\n  XML: 0x0003,\n  XML_FIRST_CHUNK: 0x0100,\n  XML_START_NAMESPACE: 0x0100,\n  XML_END_NAMESPACE: 0x0101,\n  XML_START_ELEMENT: 0x0102,\n  XML_END_ELEMENT: 0x0103,\n  XML_CDATA: 0x0104,\n  XML_LAST_CHUNK: 0x017f,\n  XML_RESOURCE_MAP: 0x0180,\n  TABLE_PACKAGE: 0x0200,\n  TABLE_TYPE: 0x0201,\n  TABLE_TYPE_SPEC: 0x0202,\n};\n\nconst StringFlags = {\n  SORTED: 1 << 0,\n  UTF8: 1 << 8,\n};\n\n// Taken from android.util.TypedValue\nconst TypedValue = {\n  COMPLEX_MANTISSA_MASK: 0x00ffffff,\n  COMPLEX_MANTISSA_SHIFT: 0x00000008,\n  COMPLEX_RADIX_0p23: 0x00000003,\n  COMPLEX_RADIX_16p7: 0x00000001,\n  COMPLEX_RADIX_23p0: 0x00000000,\n  COMPLEX_RADIX_8p15: 0x00000002,\n  COMPLEX_RADIX_MASK: 0x00000003,\n  COMPLEX_RADIX_SHIFT: 0x00000004,\n  COMPLEX_UNIT_DIP: 0x00000001,\n  COMPLEX_UNIT_FRACTION: 0x00000000,\n  COMPLEX_UNIT_FRACTION_PARENT: 0x00000001,\n  COMPLEX_UNIT_IN: 0x00000004,\n  COMPLEX_UNIT_MASK: 0x0000000f,\n  COMPLEX_UNIT_MM: 0x00000005,\n  COMPLEX_UNIT_PT: 0x00000003,\n  COMPLEX_UNIT_PX: 0x00000000,\n  COMPLEX_UNIT_SHIFT: 0x00000000,\n  COMPLEX_UNIT_SP: 0x00000002,\n  DENSITY_DEFAULT: 0x00000000,\n  DENSITY_NONE: 0x0000ffff,\n  TYPE_ATTRIBUTE: 0x00000002,\n  TYPE_DIMENSION: 0x00000005,\n  TYPE_FIRST_COLOR_INT: 0x0000001c,\n  TYPE_FIRST_INT: 0x00000010,\n  TYPE_FLOAT: 0x00000004,\n  TYPE_FRACTION: 0x00000006,\n  TYPE_INT_BOOLEAN: 0x00000012,\n  TYPE_INT_COLOR_ARGB4: 0x0000001e,\n  TYPE_INT_COLOR_ARGB8: 0x0000001c,\n  TYPE_INT_COLOR_RGB4: 0x0000001f,\n  TYPE_INT_COLOR_RGB8: 0x0000001d,\n  TYPE_INT_DEC: 0x00000010,\n  TYPE_INT_HEX: 0x00000011,\n  TYPE_LAST_COLOR_INT: 0x0000001f,\n  TYPE_LAST_INT: 0x0000001f,\n  TYPE_NULL: 0x00000000,\n  TYPE_REFERENCE: 0x00000001,\n  TYPE_STRING: 0x00000003,\n};\n\nexport class BinaryXmlParser {\n  cursor = 0;\n  strings: string[] = [];\n  resources: any[] = [];\n  document: any;\n  parent: any;\n  stack: any[] = [];\n  debug = false;\n  constructor(public buffer: Buffer, options: any = {}) {\n    this.debug = options.debug || false;\n  }\n\n  readU8(): number {\n    const val = this.buffer[this.cursor];\n    this.cursor += 1;\n    return val;\n  }\n\n  readU16(): number {\n    const val = this.buffer.readUInt16LE(this.cursor);\n    this.cursor += 2;\n    return val;\n  }\n\n  readS32(): number {\n    const val = this.buffer.readInt32LE(this.cursor);\n    this.cursor += 4;\n    return val;\n  }\n\n  readU32(): number {\n    // debug('readU32');\n    // debug('cursor:', this.cursor);\n    const val = this.buffer.readUInt32LE(this.cursor);\n    // debug('value:', val);\n    this.cursor += 4;\n    return val;\n  }\n\n  readLength8(): number {\n    // debug('readLength8');\n    let len = this.readU8();\n    if (len & 0x80) {\n      len = (len & 0x7f) << 8;\n      len += this.readU8();\n    }\n    // debug('length:', len);\n    return len;\n  }\n\n  readLength16(): number {\n    // debug('readLength16');\n    let len = this.readU16();\n    if (len & 0x8000) {\n      len = (len & 0x7fff) << 16;\n      len += this.readU16();\n    }\n    // debug('length:', len);\n    return len;\n  }\n\n  readDimension(): any {\n    // debug('readDimension');\n\n    const dimension: any = {\n      value: null,\n      unit: null,\n      rawUnit: null,\n    };\n\n    const value = this.readU32();\n    const unit = dimension.value & 0xff;\n\n    dimension.value = value >> 8;\n    dimension.rawUnit = unit;\n\n    switch (unit) {\n      case TypedValue.COMPLEX_UNIT_MM:\n        dimension.unit = 'mm';\n        break;\n      case TypedValue.COMPLEX_UNIT_PX:\n        dimension.unit = 'px';\n        break;\n      case TypedValue.COMPLEX_UNIT_DIP:\n        dimension.unit = 'dp';\n        break;\n      case TypedValue.COMPLEX_UNIT_SP:\n        dimension.unit = 'sp';\n        break;\n      case TypedValue.COMPLEX_UNIT_PT:\n        dimension.unit = 'pt';\n        break;\n      case TypedValue.COMPLEX_UNIT_IN:\n        dimension.unit = 'in';\n        break;\n    }\n\n    return dimension;\n  }\n\n  readFraction(): any {\n    const fraction: any = {\n      value: null,\n      type: null,\n      rawType: null,\n    };\n\n    const value = this.readU32();\n    const type = value & 0xf;\n\n    fraction.value = this.convertIntToFloat(value >> 4);\n    fraction.rawType = type;\n\n    switch (type) {\n      case TypedValue.COMPLEX_UNIT_FRACTION:\n        fraction.type = '%';\n        break;\n      case TypedValue.COMPLEX_UNIT_FRACTION_PARENT:\n        fraction.type = '%p';\n        break;\n    }\n\n    return fraction;\n  }\n\n  readHex24(): string {\n    const val = (this.readU32() & 0xffffff).toString(16);\n    return val;\n  }\n\n  readHex32(): string {\n    const val = this.readU32().toString(16);\n    return val;\n  }\n\n  readTypedValue(): any {\n    const typedValue: any = {\n      value: null,\n      type: null,\n      rawType: null,\n    };\n\n    const start = this.cursor;\n\n    let size = this.readU16();\n    /* const zero = */ this.readU8();\n    const dataType = this.readU8();\n\n    // Yes, there has been a real world APK where the size is malformed.\n    if (size === 0) {\n      size = 8;\n    }\n\n    typedValue.rawType = dataType;\n\n    switch (dataType) {\n      case TypedValue.TYPE_INT_DEC:\n        typedValue.value = this.readS32();\n        typedValue.type = 'int_dec';\n        break;\n      case TypedValue.TYPE_INT_HEX:\n        typedValue.value = this.readS32();\n        typedValue.type = 'int_hex';\n        break;\n      case TypedValue.TYPE_STRING: {\n        const ref = this.readS32();\n        typedValue.value = ref > 0 ? this.strings[ref] : '';\n        typedValue.type = 'string';\n        break;\n      }\n      case TypedValue.TYPE_REFERENCE: {\n        const id = this.readU32();\n        typedValue.value = `resourceId:0x${id.toString(16)}`;\n        typedValue.type = 'reference';\n        break;\n      }\n      case TypedValue.TYPE_INT_BOOLEAN:\n        typedValue.value = this.readS32() !== 0;\n        typedValue.type = 'boolean';\n        break;\n      case TypedValue.TYPE_NULL:\n        this.readU32();\n        typedValue.value = null;\n        typedValue.type = 'null';\n        break;\n      case TypedValue.TYPE_INT_COLOR_RGB8:\n        typedValue.value = this.readHex24();\n        typedValue.type = 'rgb8';\n        break;\n      case TypedValue.TYPE_INT_COLOR_RGB4:\n        typedValue.value = this.readHex24();\n        typedValue.type = 'rgb4';\n        break;\n      case TypedValue.TYPE_INT_COLOR_ARGB8:\n        typedValue.value = this.readHex32();\n        typedValue.type = 'argb8';\n        break;\n      case TypedValue.TYPE_INT_COLOR_ARGB4:\n        typedValue.value = this.readHex32();\n        typedValue.type = 'argb4';\n        break;\n      case TypedValue.TYPE_DIMENSION:\n        typedValue.value = this.readDimension();\n        typedValue.type = 'dimension';\n        break;\n      case TypedValue.TYPE_FRACTION:\n        typedValue.value = this.readFraction();\n        typedValue.type = 'fraction';\n        break;\n      default: {\n        // const type = dataType.toString(16);\n        typedValue.value = this.readU32();\n        typedValue.type = 'unknown';\n      }\n    }\n\n    // Ensure we consume the whole value\n    const end = start + size;\n    if (this.cursor !== end) {\n      // const type = dataType.toString(16);\n      // const diff = end - this.cursor;\n      //       debug(`Cursor is off by ${diff} bytes at ${this.cursor} at supposed end \\\n      // of typed value of type 0x${type}. The typed value started at offset ${start} \\\n      // and is supposed to end at offset ${end}. Ignoring the rest of the value.`);\n      this.cursor = end;\n    }\n\n    return typedValue;\n  }\n\n  // https://twitter.com/kawasima/status/427730289201139712\n  convertIntToFloat(int: number): number {\n    const buf = new ArrayBuffer(4);\n    new Int32Array(buf)[0] = int;\n    return new Float32Array(buf)[0];\n  }\n\n  readString(encoding: string): string {\n    let stringLength;\n    let byteLength;\n    let value;\n    switch (encoding) {\n      case 'utf-8':\n        stringLength = this.readLength8();\n        byteLength = this.readLength8();\n        value = this.buffer.toString(encoding, this.cursor, (this.cursor += byteLength));\n        assert.equal(this.readU8(), 0, 'String must end with trailing zero');\n        return value;\n      case 'ucs2':\n        stringLength = this.readLength16();\n        byteLength = stringLength * 2;\n        value = this.buffer.toString(encoding, this.cursor, (this.cursor += byteLength));\n        assert.equal(this.readU16(), 0, 'String must end with trailing zero');\n        return value;\n      default:\n        throw new assert.AssertionError({ message: `Unsupported encoding '${encoding}'` });\n    }\n  }\n\n  readChunkHeader(): {\n    startOffset: number;\n    chunkType: number;\n    headerSize: number;\n    chunkSize: number;\n  } {\n    return {\n      startOffset: this.cursor,\n      chunkType: this.readU16(),\n      headerSize: this.readU16(),\n      chunkSize: this.readU32(),\n    };\n  }\n\n  readStringPool(header: any): null {\n    header.stringCount = this.readU32();\n    header.styleCount = this.readU32();\n    header.flags = this.readU32();\n    header.stringsStart = this.readU32();\n    header.stylesStart = this.readU32();\n\n    if (header.chunkType !== ChunkType.STRING_POOL) {\n      throw new assert.AssertionError({ message: 'Invalid string pool header' });\n    }\n\n    const offsets = [];\n    for (let i = 0, l = header.stringCount; i < l; ++i) {\n      offsets.push(this.readU32());\n    }\n\n    const encoding = (header.flags & StringFlags.UTF8) === StringFlags.UTF8 ? 'utf-8' : 'ucs2';\n\n    const stringsStart = header.startOffset + header.stringsStart;\n    this.cursor = stringsStart;\n    for (let i = 0, l = header.stringCount; i < l; ++i) {\n      this.cursor = stringsStart + offsets[i];\n      this.strings.push(this.readString(encoding));\n    }\n\n    // Skip styles\n    this.cursor = header.startOffset + header.chunkSize;\n\n    return null;\n  }\n\n  readResourceMap(header: any): null {\n    const count = Math.floor((header.chunkSize - header.headerSize) / 4);\n    for (let i = 0; i < count; ++i) {\n      this.resources.push(this.readU32());\n    }\n    return null;\n  }\n\n  readXmlNamespaceStart(/* header */): null {\n    this.readU32();\n    this.readU32();\n    this.readU32();\n    this.readU32();\n\n    // const line = this.readU32();\n    // const commentRef = this.readU32();\n    // const prefixRef = this.readS32();\n    // const uriRef = this.readS32();\n\n    // We don't currently care about the values, but they could\n    // be accessed like so:\n    //\n    // namespaceURI.prefix = this.strings[prefixRef] // if prefixRef > 0\n    // namespaceURI.uri = this.strings[uriRef] // if uriRef > 0\n\n    return null;\n  }\n\n  readXmlNamespaceEnd(/* header */): null {\n    // debug('readXmlNamespaceEnd');\n\n    this.readU32();\n    this.readU32();\n    this.readU32();\n    this.readU32();\n\n    // const line = this.readU32();\n    // const commentRef = this.readU32();\n    // const prefixRef = this.readS32();\n    // const uriRef = this.readS32();\n\n    // We don't currently care about the values, but they could\n    // be accessed like so:\n    //\n    // namespaceURI.prefix = this.strings[prefixRef] // if prefixRef > 0\n    // namespaceURI.uri = this.strings[uriRef] // if uriRef > 0\n\n    return null;\n  }\n\n  readXmlElementStart(/* header */): any {\n    // debug('readXmlElementStart');\n\n    const node: any = {\n      namespaceURI: null,\n      nodeType: NodeType.ELEMENT_NODE,\n      nodeName: null,\n      attributes: [],\n      childNodes: [],\n    };\n\n    this.readU32();\n    this.readU32();\n    // const line = this.readU32();\n    // const commentRef = this.readU32();\n    const nsRef = this.readS32();\n    const nameRef = this.readS32();\n\n    if (nsRef > 0) {\n      node.namespaceURI = this.strings[nsRef];\n    }\n\n    node.nodeName = this.strings[nameRef];\n\n    this.readU16();\n    this.readU16();\n    // const attrStart = this.readU16();\n    // const attrSize = this.readU16();\n    const attrCount = this.readU16();\n    // const idIndex = this.readU16();\n    // const classIndex = this.readU16();\n    // const styleIndex = this.readU16();\n    this.readU16();\n    this.readU16();\n    this.readU16();\n\n    for (let i = 0; i < attrCount; ++i) {\n      node.attributes.push(this.readXmlAttribute());\n    }\n\n    if (this.document) {\n      this.parent.childNodes.push(node);\n      this.parent = node;\n    } else {\n      this.document = this.parent = node;\n    }\n\n    this.stack.push(node);\n\n    return node;\n  }\n\n  readXmlAttribute(): any {\n    // debug('readXmlAttribute');\n\n    const attr: any = {\n      namespaceURI: null,\n      nodeType: NodeType.ATTRIBUTE_NODE,\n      nodeName: null,\n      name: null,\n      value: null,\n      typedValue: null,\n    };\n\n    const nsRef = this.readS32();\n    const nameRef = this.readS32();\n    const valueRef = this.readS32();\n\n    if (nsRef > 0) {\n      attr.namespaceURI = this.strings[nsRef];\n    }\n\n    attr.nodeName = attr.name = this.strings[nameRef];\n\n    if (valueRef > 0) {\n      attr.value = this.strings[valueRef];\n    }\n\n    attr.typedValue = this.readTypedValue();\n\n    return attr;\n  }\n\n  readXmlElementEnd(/* header */): null {\n    // debug('readXmlCData');\n\n    this.readU32();\n    this.readU32();\n    this.readU32();\n    this.readU32();\n    // const line = this.readU32();\n    // const commentRef = this.readU32();\n    // const nsRef = this.readS32();\n    // const nameRef = this.readS32();\n\n    this.stack.pop();\n    this.parent = this.stack[this.stack.length - 1];\n\n    return null;\n  }\n\n  readXmlCData(/* header */): any {\n    // debug('readXmlCData');\n\n    const cdata: any = {\n      namespaceURI: null,\n      nodeType: NodeType.CDATA_SECTION_NODE,\n      nodeName: '#cdata',\n      data: null,\n      typedValue: null,\n    };\n\n    this.readU32();\n    this.readU32();\n    // const line = this.readU32();\n    // const commentRef = this.readU32();\n    const dataRef = this.readS32();\n\n    if (dataRef > 0) {\n      cdata.data = this.strings[dataRef];\n    }\n\n    cdata.typedValue = this.readTypedValue();\n\n    this.parent.childNodes.push(cdata);\n\n    return cdata;\n  }\n\n  readNull(header: any): null {\n    // debug('readNull');\n    this.cursor += header.chunkSize - header.headerSize;\n    return null;\n  }\n\n  parse(): any {\n    // debug('parse');\n\n    const xmlHeader = this.readChunkHeader();\n    if (xmlHeader.chunkType !== ChunkType.XML) {\n      throw new assert.AssertionError({ message: 'Invalid XML header' });\n    }\n\n    while (this.cursor < this.buffer.length) {\n      // debug('chunk');\n      const start = this.cursor;\n      const header = this.readChunkHeader();\n      switch (header.chunkType) {\n        case ChunkType.STRING_POOL:\n          this.readStringPool(header);\n          break;\n        case ChunkType.XML_RESOURCE_MAP:\n          this.readResourceMap(header);\n          break;\n        case ChunkType.XML_START_NAMESPACE:\n          this.readXmlNamespaceStart();\n          break;\n        case ChunkType.XML_END_NAMESPACE:\n          this.readXmlNamespaceEnd();\n          break;\n        case ChunkType.XML_START_ELEMENT:\n          this.readXmlElementStart();\n          break;\n        case ChunkType.XML_END_ELEMENT:\n          this.readXmlElementEnd();\n          break;\n        case ChunkType.XML_CDATA:\n          this.readXmlCData();\n          break;\n        case ChunkType.NULL:\n          this.readNull(header);\n          break;\n        default:\n          throw new assert.AssertionError({\n            message: `Unsupported chunk type '${header.chunkType}'`,\n          });\n      }\n\n      // Ensure we consume the whole chunk\n      const end = start + header.chunkSize;\n      if (this.cursor !== end) {\n        // const diff = end - this.cursor;\n        // const type = header.chunkType.toString(16);\n        // debug(`Cursor is off by ${diff} bytes at ${this.cursor} at supposed \\\n        // end of chunk of type 0x${type}. The chunk started at offset ${start} and is \\\n        // supposed to end at offset ${end}. Ignoring the rest of the chunk.`);\n        //         this.cursor = end;\n      }\n    }\n\n    return this.document;\n  }\n}\n"]}