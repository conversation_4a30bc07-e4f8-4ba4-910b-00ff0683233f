{"name": "@react-native/gradle-plugin", "version": "0.72.11", "description": "⚛️ G<PERSON>le Plugin for React Native", "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/react-native-gradle-plugin", "repository": {"type": "git", "url": "**************:facebook/react-native.git", "directory": "packages/react-native-gradle-plugin"}, "scripts": {"build": "./gradlew build", "clean": "./gradlew clean", "test": "./gradlew check"}, "license": "MIT", "files": ["settings.gradle.kts", "build.gradle.kts", "gradle", "gradlew", "gradlew.bat", "src/main", "README.md"], "dependencies": {}, "devDependencies": {}}