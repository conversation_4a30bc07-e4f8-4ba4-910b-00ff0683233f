"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "createExoticTransformer", {
  enumerable: true,
  get: function () {
    return _createExoticTransformer().createExoticTransformer;
  }
});
Object.defineProperty(exports, "createModuleMatcher", {
  enumerable: true,
  get: function () {
    return _createMatcher().createModuleMatcher;
  }
});
Object.defineProperty(exports, "createMultiRuleTransformer", {
  enumerable: true,
  get: function () {
    return _createMultiRuleTransformer().createMultiRuleTransformer;
  }
});
Object.defineProperty(exports, "getCacheKey", {
  enumerable: true,
  get: function () {
    return _getCacheKey().getCacheKey;
  }
});
Object.defineProperty(exports, "loaders", {
  enumerable: true,
  get: function () {
    return _createMultiRuleTransformer().loaders;
  }
});
function _createExoticTransformer() {
  const data = require("./createExoticTransformer");
  _createExoticTransformer = function () {
    return data;
  };
  return data;
}
function _getCacheKey() {
  const data = require("./getCacheKey");
  _getCacheKey = function () {
    return data;
  };
  return data;
}
function _createMultiRuleTransformer() {
  const data = require("./createMultiRuleTransformer");
  _createMultiRuleTransformer = function () {
    return data;
  };
  return data;
}
function _createMatcher() {
  const data = require("./createMatcher");
  _createMatcher = function () {
    return data;
  };
  return data;
}
//# sourceMappingURL=index.js.map