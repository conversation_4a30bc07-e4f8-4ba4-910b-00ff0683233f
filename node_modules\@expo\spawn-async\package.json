{"name": "@expo/spawn-async", "version": "1.5.0", "description": "A Promise-based interface into processes created by child_process.spawn", "main": "index.js", "types": "./build/spawnAsync.d.ts", "files": ["build"], "engines": {"node": ">=4"}, "scripts": {"build": "tsc", "clean": "rm -rf build", "prepare": "yarn clean && yarn build", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/expo/spawn-async.git"}, "keywords": ["spawn", "child_process", "async", "promise", "process"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/spawn-async/issues"}, "homepage": "https://github.com/expo/spawn-async#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "rootDir": "src"}, "dependencies": {"cross-spawn": "^6.0.5"}, "devDependencies": {"@types/cross-spawn": "^6.0.0", "@types/jest": "^23.3.9", "jest": "^23.6.0", "ts-jest": "^23.10.4", "typescript": "^3.1.6"}}