<p align="center">
  <img src="https://raw.githubusercontent.com/tabrindle/envinfo/master/logo.png" align="center"  width="700px"/>
  <h3 align="center">envinfo generates a report of the common details needed when troubleshooting software issues, such as your operating system, binary versions, browsers, installed languages, and more</h3>
  <hr/>
</p>

[![npm version](https://badge.fury.io/js/envinfo.svg)](https://badge.fury.io/js/envinfo) [![npm downloads per month](https://img.shields.io/npm/dm/envinfo.svg?maxAge=86400)](https://www.npmjs.com/package/envinfo) [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![All Contributors](https://img.shields.io/badge/all_contributors-12-orange.svg?style=flat-square)](#contributors)

## The problem

-   It works on my computer
-   "command not found"
-   what version of "command" are you running?
-   what version of "different command" are you running?
-   do you have "insert obscure android sdk version"?
-   every github issue reporting template ever:

**Please mention other relevant information such as the browser version, Node.js version, Operating System and programming language.**

## This solution

-   Gather all of this information in one spot, quickly, and painlessly.

## Installation

To use as a CLI tool, install this package globally:

```sh
npm install -g envinfo || yarn global add envinfo
```

Or, use without installing with npx:

`npx envinfo`

To use as a library in another project:

```sh
npm install envinfo || yarn add envinfo
```

## CLI Usage

`envinfo` || `npx envinfo`

```bash
  System:
    OS: macOS Mojave 10.14.5
    CPU: (8) x64 Intel(R) Core(TM) i7-7820HQ CPU @ 2.90GHz
    Memory: 2.97 GB / 16.00 GB
    Shell: 5.3 - /bin/zsh
  Binaries:
    Node: 8.16.0 - ~/.nvm/versions/node/v8.16.0/bin/node
    Yarn: 1.15.2 - ~/.yarn/bin/yarn
    npm: 6.9.0 - ~/.nvm/versions/node/v8.16.0/bin/npm
    pnpm: 8.7.6 - /usr/local/bin/pnpm
    bun: 1.0.2 - /usr/local/bin/bun
    Watchman: 4.9.0 - /usr/local/bin/watchman
  Managers:
    Cargo: 1.31.0 - ~/.cargo/bin/cargo
    CocoaPods: 1.7.3 - /usr/local/bin/pod
    Composer: 1.8.6 - /usr/local/bin/composer
    Gradle: 5.5 - /usr/local/bin/gradle
    Homebrew: 2.1.7 - /usr/local/bin/brew
    Maven: 3.6.1 - /usr/local/bin/mvn
    pip2: 19.0.3 - /usr/local/bin/pip2
    pip3: 19.0.2 - /usr/local/bin/pip3
    RubyGems: ******* - /usr/bin/gem
  Utilities:
    CMake: 3.13.3 - /usr/local/bin/cmake
    Make: 3.81 - /usr/bin/make
    GCC: 10.14. - /usr/bin/gcc
    Git: 2.20.0 - /usr/local/bin/git
    Mercurial: 4.5.3 - /usr/bin/hg
    Clang: 1001.0.46.4 - /usr/bin/clang
    Subversion: 1.10.3 - /usr/bin/svn
  Servers:
    Apache: 2.4.34 - /usr/sbin/apachectl
    Nginx: 1.13.12 - /usr/local/bin/nginx
  Virtualization:
    Docker: 18.09.1 - /usr/local/bin/docker
    Parallels: 13.3.0 - /usr/local/bin/prlctl
    VirtualBox: 5.2.20 - /usr/local/bin/vboxmanage
  SDKs:
    iOS SDK:
      Platforms: iOS 12.2, macOS 10.14, tvOS 12.2, watchOS 5.2
    Android SDK:
      API Levels: 28
      Build Tools: 28.0.3
      System Images: android-28 | Google Play Intel x86 Atom
  IDEs:
    Android Studio: 3.2 AI-181.5540.7.32.5056338
    Atom: 1.23.3
    Emacs: 22.1.1 - /usr/bin/emacs
    Nano: 2.0.6 - /usr/bin/nano
    VSCode: 1.36.0 - /usr/local/bin/code
    Vim: 8.0 - /usr/bin/vim
    Xcode: 10.2.1/10E1001 - /usr/bin/xcodebuild
  Languages:
    Bash: 4.4.23 - /usr/local/bin/bash
    Elixir: 1.6.2 - /usr/local/bin/elixir
    Go: 1.11.1 - /usr/local/bin/go
    Java: 1.8.0_192 - /usr/bin/javac
    Perl: 5.18.4 - /usr/bin/perl
    PHP: 7.1.23 - /usr/bin/php
    Python: 2.7.16 - /usr/local/bin/python
    Python3: 3.7.2 - /usr/local/bin/python3
    R: 3.6.0 - /usr/local/bin/R
    Ruby: 2.3.7 - /usr/bin/ruby
    Rust: 1.16.0 - /Users/<USER>/.cargo/bin/rustup
  Databases:
    MongoDB: 3.6.4 - /usr/local/bin/mongo
    MySQL: 10.3.10 (MariaDB) - /usr/local/bin/mysql
    PostgreSQL: 10.3 - /usr/local/bin/postgres
    SQLite: 3.24.0 - /usr/bin/sqlite3
  Browsers:
    Chrome: 75.0.3770.100
    Chrome Canary: 77.0.3847.0
    Firefox: 68.0
    Firefox Developer Edition: 69.0
    Firefox Nightly: 69.0a1
    Safari: 12.1.1
    Safari Technology Preview: 13.0
  npmPackages:
    apollo-client: ^2.3.1 => 2.3.1
    jest: ^22.2.1 => 22.2.1
    ...
    react: ^16.3.2 => 16.3.2
    react-apollo: ^2.1.4 => 2.1.4
    run4staged: ^1.1.1 => 1.1.1
    solidarity: 2.0.5 => 2.0.5
    styled-components: ^3.1.6 => 3.1.6
  npmGlobalPackages:
    create-react-app: 1.5.2
    create-react-native-app: 1.0.0
    envinfo: 5.10.0
    exp: 49.2.2
    gatsby-cli: 1.1.52
    npm: 5.6.0
    react-native-cli: 2.0.1
    solidarity: 2.1.0
    typescript: 2.8.1
```

## Programmatic Usage

Envinfo takes a configuration object and returns a Promise that resolves a string (optionally yaml, json or markdown)

```javascript
import envinfo from 'envinfo';

envinfo.run(
    {
        System: ['OS', 'CPU'],
        Binaries: ['Node', 'Yarn', 'npm'],
        Browsers: ['Chrome', 'Firefox', 'Safari'],
        npmPackages: ['styled-components', 'babel-plugin-styled-components'],
    },
    { json: true, showNotFound: true }
).then(env => console.log(env));
```

logs:

```json
{
    "System": {
        "OS": "macOS High Sierra 10.13",
        "CPU": "x64 Intel(R) Core(TM) i7-4870HQ CPU @ 2.50GHz"
    },
    "Binaries": {
        "Node": {
            "version": "8.11.0",
            "path": "~/.nvm/versions/node/v8.11.0/bin/node"
        },
        "Yarn": {
            "version": "1.5.1",
            "path": "~/.yarn/bin/yarn"
        },
        "npm": {
            "version": "5.6.0",
            "path": "~/.nvm/versions/node/v8.11.0/bin/npm"
        }
    },
    "Browsers": {
        "Chrome": {
            "version": "67.0.3396.62"
        },
        "Firefox": {
            "version": "59.0.2"
        },
        "Safari": {
            "version": "11.0"
        }
    },
    "npmPackages": {
        "styled-components": {
            "wanted": "^3.2.1",
            "installed": "3.2.1"
        },
        "babel-plugin-styled-components": "Not Found"
    }
}
```

All of envinfo's helpers are also exported for use. You can use envinfo as a whole, or just the parts that you need, like this:

```javascript
const envinfo = require('envinfo');

// each helper returns a promise
const node = await envinfo.helpers.getNodeInfo();

// The promises resolve to an array of values: ["Name", "Version", "Path"]
// e.g. ["Node", "10.9.0", "/usr/local/bin/node"]

console.log(`Node: ${node[1]} - ${node[2]}`); // "Node: 10.9.0 - ~/.nvm/versions/node/v8.14.0/bin/node"
```

## CLI Options

```
    --system               Print general system info such as OS, CPU, Memory and Shell
    --browsers             Get version numbers of installed web browsers
    --SDKs                 Get platforms, build tools and SDKs of iOS and Android
    --IDEs                 Get version numbers of installed IDEs
    --languages            Get version numbers of installed languages such as Java, Python, PHP, etc
    --binaries             Get version numbers of node, npm, watchman, etc
    --npmPackages          Get version numbers of locally installed npm packages - glob, string, or comma delimited list
    --npmGlobalPackages    Get version numbers of globally installed npm packages

    --duplicates           Mark duplicate npm packages inside parentheses eg. (2.1.4)
    --fullTree             Traverse entire node_modules dependency tree, not just top level

    --markdown             Print output in markdown format
    --json                 Print output in JSON format
    --console              Print to console (defaults to on for CLI usage, off for programmatic usage)
```

## Integration

envinfo is live in:

-   [React Native](https://github.com/facebook/react-native) (`react-native info`)
-   [Create React App](https://github.com/facebook/create-react-app) (`create-react-app --info`)
-   [Expo Environment Info](https://github.com/expo/expo-cli/tree/main/packages/expo-env-info) (`npx expo-env-info`)
-   [Webpack](https://github.com/webpack/webpack-cli) (`webpack-cli info`)
-   [Solidarity](https://github.com/infinitered/solidarity) (`solidarity report`)
-   [Gatsby](https://github.com/gatsbyjs/gatsby) (`gatsby info`)

envinfo is used in the ISSUE_TEMPLATE of:

-   [styled-components](https://github.com/styled-components/styled-components)
-   [Jest](https://github.com/facebook/jest)
-   [Apollo Client](https://github.com/apollographql/apollo-client)

## Alternatives

-   type `command -v` until you smash your computer
-   [screenfetch](https://github.com/KittyKatt/screenFetch) - fetch system and terminal information, and display a pretty ascii logo
-   [Solidarity](https://github.com/infinitered/solidarity) - a project based environment checker
-   write your own

## License

MIT

## Contributing

PRs for additional features are welcome! Run `npm run lint && npm run format` before committing.

This project came out of a [PR](https://github.com/facebook/react-native/pull/14428) to the React Native CLI tool - issues are reported frequently without important environment information, like Node/npm versions.

## Contributors

Thanks goes to these wonderful people ([emoji key](https://github.com/kentcdodds/all-contributors#emoji-key)):

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore -->
| [<img src="https://avatars1.githubusercontent.com/u/2925048?v=4" width="100px;"/><br /><sub><b>Trevor Brindle</b></sub>](http://trevorbrindle.com)<br />[💬](#question-tabrindle "Answering Questions") [📝](#blog-tabrindle "Blogposts") [🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Atabrindle "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=tabrindle "Code") [📖](https://github.com/tabrindle/envinfo/commits?author=tabrindle "Documentation") [💡](#example-tabrindle "Examples") [🤔](#ideas-tabrindle "Ideas, Planning, & Feedback") [👀](#review-tabrindle "Reviewed Pull Requests") [📢](#talk-tabrindle "Talks") [⚠️](https://github.com/tabrindle/envinfo/commits?author=tabrindle "Tests") | [<img src="https://avatars0.githubusercontent.com/u/997157?v=4" width="100px;"/><br /><sub><b>Gant Laborde</b></sub>](http://gantlaborde.com/)<br />[📝](#blog-GantMan "Blogposts") [🐛](https://github.com/tabrindle/envinfo/issues?q=author%3AGantMan "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=GantMan "Code") [🤔](#ideas-GantMan "Ideas, Planning, & Feedback") | [<img src="https://avatars1.githubusercontent.com/u/599352?v=4" width="100px;"/><br /><sub><b>Anton Fisher</b></sub>](http://antonfisher.com)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Aantonfisher "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=antonfisher "Code") | [<img src="https://avatars1.githubusercontent.com/u/960133?v=4" width="100px;"/><br /><sub><b>Ahmad Awais ⚡️</b></sub>](https://AhmadAwais.com/)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Aahmadawais "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=ahmadawais "Code") | [<img src="https://avatars2.githubusercontent.com/u/9251453?v=4" width="100px;"/><br /><sub><b>Hasan</b></sub>](https://github.com/LEQADA)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3ALEQADA "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=LEQADA "Code") | [<img src="https://avatars3.githubusercontent.com/u/1232725?v=4" width="100px;"/><br /><sub><b>Ernesto Ramírez</b></sub>](http://twitter.com/_ErnestoR)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3AErnestoR "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=ErnestoR "Code") | [<img src="https://avatars1.githubusercontent.com/u/3759816?v=4" width="100px;"/><br /><sub><b>Jiawen Geng</b></sub>](https://www.gengjiawen.com)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Agengjiawen "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=gengjiawen "Code") [🤔](#ideas-gengjiawen "Ideas, Planning, & Feedback") [⚠️](https://github.com/tabrindle/envinfo/commits?author=gengjiawen "Tests") |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: |
| [<img src="https://avatars3.githubusercontent.com/u/12520493?v=4" width="100px;"/><br /><sub><b>Zac Anger</b></sub>](https://zacanger.com)<br />[💻](https://github.com/tabrindle/envinfo/commits?author=zacanger "Code") [🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Azacanger "Bug reports") | [<img src="https://avatars3.githubusercontent.com/u/497214?v=4" width="100px;"/><br /><sub><b>Ville Immonen</b></sub>](https://twitter.com/VilleImmonen)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Afson "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=fson "Code") | [<img src="https://avatars2.githubusercontent.com/u/27246?v=4" width="100px;"/><br /><sub><b>Olmo Maldonado</b></sub>](http://ibolmo.com)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Aibolmo "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=ibolmo "Code") | [<img src="https://avatars.githubusercontent.com/u/15812317?v=4" width="100px;"/><br /><sub><b>Rishabh Chawla</b></sub>](https://rishabhchawla.co)<br />[🐛](https://github.com/tabrindle/envinfo/issues?q=author%3Arishabh3112 "Bug reports") [💻](https://github.com/tabrindle/envinfo/commits?author=rishabh3112 "Code") | [<img src="https://avatars.githubusercontent.com/u/174297?v=4" width="100px;"/><br /><sub><b>Carl Taylor</b></sub>](https://github.com/Nthalk)<br />[💻](https://github.com/tabrindle/envinfo/commits?author=Nthalk "Code") |
<!-- ALL-CONTRIBUTORS-LIST:END -->

This project follows the [all-contributors](https://github.com/kentcdodds/all-contributors) specification. Contributions of any kind welcome!
