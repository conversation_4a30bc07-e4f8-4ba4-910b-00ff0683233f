{"version": 3, "file": "nodeWorkspaces.js", "sourceRoot": "", "sources": ["../../src/utils/nodeWorkspaces.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAA6C;AAC7C,wFAAwE;AACxE,4CAAoB;AACpB,sDAA2B;AAC3B,4DAAoC;AACpC,gDAAwB;AAEX,QAAA,aAAa,GAAG,mBAAmB,CAAC;AACpC,QAAA,cAAc,GAAG,WAAW,CAAC;AAC7B,QAAA,cAAc,GAAG,gBAAgB,CAAC;AAClC,QAAA,mBAAmB,GAAG,qBAAqB,CAAC;AAC5C,QAAA,aAAa,GAAG,WAAW,CAAC;AAEzC,qHAAqH;AACrH,SAAgB,0BAA0B,CAAC,WAAmB;IAC5D,IAAI;QACF,OAAO,IAAA,kCAAgC,EAAC,WAAW,CAAC,CAAC;KACtD;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YAC1D,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AATD,gEASC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,WAAmB;IACvD,MAAM,gBAAgB,GAAG,0BAA0B,CAAC;IACpD,MAAM,iBAAiB,GACrB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC;IAE/E,MAAM,aAAa,GAAG,iBAAiB;QACrC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,2BAAmB,CAAC;QACnD,CAAC,CAAC,IAAA,cAAU,EAAC,2BAAmB,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;IAE1D,IAAI,CAAC,aAAa,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;QACnD,OAAO,IAAI,CAAC;KACb;IAED,IAAI;QACF,2CAA2C;QAC3C,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,iBAAI,CAAC,IAAI,CAAC,YAAE,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;QACnF,yHAAyH;QACzH,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAE/D,IAAI,YAAY,KAAK,EAAE,IAAI,IAAA,oBAAU,EAAC,CAAC,YAAY,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5E,OAAO,aAAa,CAAC;SACtB;KACF;IAAC,MAAM;QACN,gCAAgC;QAChC,OAAO,IAAI,CAAC;KACb;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA7BD,sDA6BC"}