# restore-cursor [![Build Status](https://travis-ci.org/sindresorhus/restore-cursor.svg?branch=master)](https://travis-ci.org/sindresorhus/restore-cursor)

> Gracefully restore the CLI cursor on exit

Prevent the cursor you've hidden interactively from remaining hidden if the process crashes.


## Install

```
$ npm install restore-cursor
```


## Usage

```js
const restoreCursor = require('restore-cursor');

restoreCursor();
```


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
