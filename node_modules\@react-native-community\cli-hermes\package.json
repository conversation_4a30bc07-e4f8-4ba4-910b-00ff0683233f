{"name": "@react-native-community/cli-hermes", "version": "11.3.7", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-platform-android": "11.3.7", "@react-native-community/cli-tools": "11.3.7", "chalk": "^4.1.2", "hermes-profile-transformer": "^0.0.6", "ip": "^1.1.5"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "11.3.7", "@types/ip": "^1.1.0"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-hermes", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-hermes"}, "gitHead": "7a0e1772eb3ff6d8a0c4368893de0ce786f9daae"}