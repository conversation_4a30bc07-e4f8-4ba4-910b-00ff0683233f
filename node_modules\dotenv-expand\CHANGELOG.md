# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [Unreleased](https://github.com/motdotla/dotenv-expand/compare/v10.0.0...master)

## [10.0.0](https://github.com/motdotla/dotenv-expand/compare/v9.0.0...v10.0.0) (2022-12-16)

### Added

- Support special characters in default expansion ([#74](https://github.com/motdotla/dotenv-expand/pull/74))

## [9.0.0](https://github.com/motdotla/dotenv-expand/compare/v8.0.3...v9.0.0) (2022-08-30)

### Added

- Proper support for preload and cli args ([#78](https://github.com/motdotla/dotenv-expand/pull/78))

## [8.0.3](https://github.com/motdotla/dotenv-expand/compare/v8.0.2...v8.0.3) (2022-03-21)

### Changed

- 🐞 Fixed defaults bug ([#71](https://github.com/motdotla/dotenv-expand/pull/71))

## [8.0.2](https://github.com/motdotla/dotenv-expand/compare/v8.0.1...v8.0.2) (2022-03-11)

### Changed

- 🐞 Fixed preloading bug

## [8.0.1](https://github.com/motdotla/dotenv-expand/compare/v8.0.0...v8.0.1) (2022-02-03)

### Added

- Added config.js to package.json lookups

## [8.0.0](https://github.com/motdotla/dotenv-expand/compare/v7.0.0...v8.0.0) (2022-02-03)

### Changed

- _Breaking:_ Bump to `v16.0.0` of dotenv

### Added

- Preload support 🎉 ([#31](https://github.com/motdotla/dotenv-expand/pull/31))

## [7.0.0](https://github.com/motdotla/dotenv-expand/compare/v6.0.1...v7.0.0) (2022-01-17)

### Changed

- _Breaking:_ Bump to `v15.0.0` of dotenv

## [6.0.1](https://github.com/motdotla/dotenv-expand/compare/v6.0.0...v6.0.1) (2022-01-17)

### Changed

- Updated README

## [6.0.0](https://github.com/motdotla/dotenv-expand/compare/v5.1.0...v6.0.0) (2022-01-17)

### Changed

- _Breaking_ Move default export to export of `expand` function ([#14b1f2](https://github.com/motdotla/dotenv-expand/commit/14b1f28f608bc73450dca8c5aaf3a1e4f65e09ca))

### Added

- Add default expansion 🎉 ([#39](https://github.com/motdotla/dotenv-expand/pull/39))
- Add missing type descriptions

## 5.1.0 and prior

Please see commit history.




